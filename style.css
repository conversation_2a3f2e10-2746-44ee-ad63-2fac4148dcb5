/* Designer buttons */
/* Push button Home */
#btn_Home {
    padding-left: 10px;
    text-align: left;
}
#btn_Home:hover{
	background-color: rgb(66, 66, 99);
}
#btn_Home:pressed{
	border-color: rgb(66, 66, 99);
}

/* Push button Manual */
#btn_Manual {
    padding-left: 10px;
    text-align: left;
}
#btn_Manual:hover{
    background-color: rgb(66, 66, 99);
}
#btn_Manual:pressed{
    border-color: rgb(66, 66, 99);
}

/* Push button Setting */
#btn_Setting {
    padding-left: 10px;
    text-align: left;
} 
#btn_Setting:hover{
    background-color: rgb(66, 66, 99);
}
#btn_Setting:pressed{
    border-color: rgb(66, 66, 99);
}

/* Push button User */
#btn_User {
    padding-left: 10px;
    text-align: left;
}
#btn_User:hover{
    background-color: rgb(66, 66, 99);
}
#btn_User:pressed{
    border-color: rgb(66, 66, 99);
}
