import snap7
from snap7.util import*
import time
import threading
import logging
from threading import Lock
from PyQt5.QtWidgets import *
from PyQt5.QtGui import  *
from PyQt5.QtCore import *


logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
# plc = snap7.client.Client()
# ip = "***********"  
# rack = 0           
# slot = 1          
# db_number = 1    
# byte_index = 0

class PLC_Thread(QThread):
    connected = pyqtSignal(bool)
    error = pyqtSignal(str)
    update_plc_data_signal = pyqtSignal(float, float, float, float, float, float, float, float, int, int, int, int)

    def __init__(self):
        super().__init__()
        self.plc_lock = Lock()
        self.plc = snap7.client.Client()
        self.ip = "***********"
        self.rack = 0
        self.slot = 1
        self.db_number = 1
        self.byte_index = 0
        self.plc_connected = False
        self.Running = False
        self.last_cX = None
        self.last_cY = None
        
    def run(self):
        self.Running = True
        while self.Running:
            self.connectPLC()
            self.read_plc_data()
            self.START_Mode()
            self.STOP_Mode()
            self.RESET_Mode()
            self.HOME_Mode()
            time.sleep(0.1)
            pass
    
    def display_first_box(self, center_x, center_y):
        print(f"Receive signal: {center_x}, {center_y}")
        self.last_cX, self.last_cY = self.write_coordinates_to_plc(center_x, center_y, self.last_cX, self.last_cY)
    
    def display_second_code(self, code):
        print(f"Receive signal: {code}\n")
        if code == "https://qrco.de/bfZ6oj/01/00000011142024":
            self.write_number2_to_plc()
            print(f"Number to plc: 2")
        
        if code == "https://qrco.de/bfZ6mR/01/00000014112024":
            self.write_number3_to_plc()
            print(f"Number to plc: 3")
        
    def send_input_to_plc(self, data_x, data_y, data_z):
        try:
            with self.plc_lock:
                if self.plc.get_connected():
                    reading = bytearray(6)
                    snap7.util.set_int(reading, 0, data_x)
                    snap7.util.set_int(reading, 2, data_y)
                    snap7.util.set_int(reading, 4, data_z)
                    self.plc.db_write(1, 90, reading)
                    print(f"Sending input to PLC: {data_x}, {data_y}, {data_z}")
                    time.sleep(0.2)
                else:
                    print("PLC is not connected.")
        except Exception as e:
            print(f"Error sending input to PLC: {e}")
            
    def write_coordinates_to_plc(self, cX, cY, last_cX=None, last_cY=None):
        try:
            with self.plc_lock:
                if self.plc.get_connected():
                    if cX != last_cX or cY != last_cY:
                        reading = bytearray(4)
                        snap7.util.set_int(reading, 0, cX)
                        snap7.util.set_int(reading, 2, cY)
                        
                        self.plc.db_write(1, 84, reading)
                        print(f"Coordinates updated: cX={cX}, cY={cY}")
                        time.sleep(0.2)
                        return cX, cY
                    else:
                        print("No change in coordinates. Skipping write.")
        except Exception as e:
            print(f"Error writing coordinates to PLC: {e}")
        return last_cX, last_cY

    def write_number2_to_plc(self):
        try:
            with self.plc_lock:
                if self.plc.get_connected():
                    reading = bytearray(2)
                    snap7.util.set_int(reading, 0, 2)
                    self.plc.db_write(1, 88, reading)
                    time.sleep(0.2)
        except Exception as e:
            print(f"Error writing number to PLC: {e}")
    
    def write_number3_to_plc(self):
        try:
            with self.plc_lock:
                if self.plc.get_connected():
                    reading = bytearray(2)
                    snap7.util.set_int(reading, 0, 3)
                    self.plc.db_write(1, 88, reading)
                    time.sleep(0.2)
        except Exception as e:
            print(f"Error writing number to PLC: {e}")

    def connectPLC(self):
        try:
            self.plc.connect(self.ip, self.rack, self.slot)
            if self.plc.get_connected():
                print("Connected to PLC Successfully!")
                self.plc_connected = True
                self.connected.emit(True)
            else:
                print("Connect PLC Failed!")
                self.connected.emit(False)
        except Exception as e:
            print(f"Connect PLC Failed! Error: {e}")
            self.error.emit(f"Connect PLC Failed! Error: {e}")

    def disconnectPLC(self):
        try:
            with self.plc_lock:
                if self.plc_connected == True:
                    self.plc.disconnect()
                    print("Disconnected from PLC successfully.")
                    self.plc_connected = False
                    self.connected.emit(False)
                else:
                    print("PLC is already disconnected.")
                    self.connected.emit(False)
        except Exception as e:
            print(f"Disconnect PLC Failed! Error: {e}")
            self.error.emit(f"Disconnect PLC Failed! Error: {e}")
    
    def read_plc_data(self):
        while self.plc_connected == True:
            try:
                with self.plc_lock:
                    delta_x = get_real(self.plc.db_read(1, 52, 4), 0)
                    delta_y = get_real(self.plc.db_read(1, 56, 4), 0)
                    delta_z = get_real(self.plc.db_read(1, 60, 4), 0)
                    axis_x = get_real(self.plc.db_read(1, 64, 4), 0)
                    axis_y = get_real(self.plc.db_read(1, 68, 4), 0)
                    axis_z = get_real(self.plc.db_read(1, 72, 4), 0)
                    tbTocdoBT = get_real(self.plc.db_read(1, 76, 4), 0)
                    tbTocdoRB = get_real(self.plc.db_read(1, 80, 4), 0)
                    Pos_X = get_int(self.plc.db_read(1, 84, 2), 0)
                    Pos_Y = get_int(self.plc.db_read(1, 86, 2), 0)
                    SLuong = get_int(self.plc.db_read(1, 46, 2), 0)
                    number = get_int(self.plc.db_read(1, 88, 2), 0)
                    self.update_plc_data_signal.emit(delta_x, delta_y, delta_z, axis_x, axis_y, axis_z, tbTocdoBT, tbTocdoRB, Pos_X, Pos_Y, SLuong, number)
                    time.sleep(0.4)
            except Exception as e:
                if "CLI : Job pending" in str(e):
                    time.sleep(1)
                else:
                    print(f"Error reading PLC data: {e}")
                    self.error.emit(f"Error reading PLC data: {e}")
    
    def START_Mode(self):
        try:
            if self.plc_connected == True:
                db_number = 1
                byte_offset = 18
                bit_position = 1

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Start signal sent. Bit 18.1 is now ON.")
                        else:
                            print("Bit 18.1 is already ON. No action taken.")

                        break

                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(0.5)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")

        except Exception as e:
            print(f"Error sending start signal: {e}")
            self.error.emit(f"Error sending start signal: {e}")

    def STOP_Mode(self):
        try:
            if self.plc_connected == True:
                db_number = 1
                byte_offset = 18
                bit_position = 2 

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Stop signal sent. Bit 18.2 is now ON.")
                        else:
                            print("Bit 18.2 is already ON. No action taken.")
                        break
                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(0.5)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
        except Exception as e:
            print(f"Error sending stop signal: {e}")
            self.error.emit(f"Error sending stop signal: {e}")

    def RESET_Mode(self):
        try:
            if self.plc_connected == True:
                db_number = 1
                byte_offset = 18
                bit_position = 3 

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Reset signal sent. Bit 18.3 is now ON.")
                        else:
                            print("Bit 18.3 is already ON. No action taken.")
                        break
                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(0.5)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
        except Exception as e:
            print(f"Error sending reset signal: {e}")
            self.error.emit(f"Error sending reset signal: {e}")

    def HOME_Mode(self):
        try:
            if self.plc_connected == True:
                db_number = 1
                byte_offset = 18
                bit_position = 4

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Home signal sent. Bit 18.4 is now ON.")
                        else:
                            print("Bit 18.4 is already ON. No action taken.")
                        break
                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(0.5)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
        except Exception as e:
            print(f"Error sending home signal: {e}")
            self.error.emit(f"Error sending home signal: {e}")
    
    def stop(self):
        self.disconnectPLC()
        self.Running = False