from PyQt5.QtWidgets import *
from PyQt5.QtGui import  *
from PyQt5.QtCore import *
import pypylon.pylon as pylon
from pyzbar import pyzbar
import sys
import cv2
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
import icon_rc
import snap7
from snap7.util import get_bool, get_real, set_bool, set_int
import time
from UI_form import Ui_MainWindow
from CamOperation_class import CameraOperation
import ctypes
import os
import threading
import logging



logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

plc = snap7.client.Client()
ip = "***********"  
rack = 0           
slot = 1          
db_number = 1    
byte_index = 0

def TxtWrapBy(start_str, end_str, all_str):
    start = all_str.find(start_str)
    if start == -1:
        return None  
    start += len(start_str)
    end = all_str.find(end_str, start)
    if end == -1:
        return None
    return all_str[start:end].strip()

def ToHexStr(num):
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    if num == 0:
        return "0"
    if num < 0:
        num = num + 2 ** 32
    hexStr = ""
    while num > 0:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    return hexStr

class PLC_Thread(QThread):
    connected = pyqtSignal(bool)
    error = pyqtSignal(str)
    update_plc_data_signal = pyqtSignal(float, float, float, float, float, float, float, float)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.plc_lock = threading.Lock()
        self.plc = plc
        self.parent = parent
        self.plc_connected = False
        # self.system_active = self.ui.system_active
        self.Running = True
        # self.label_error = self.ui.label_error
        # self.tableWidget_out = self.ui.tableWidget_out
        # self.PoY_X = self.ui.Poy_X
        # self.PoY_Y = self.ui.Poy_Y

    def run(self):
        while self.Running:
            self.connectPLC()
            self.read_plc_data()
            self.START_Mode()
            self.STOP_Mode()
            self.RESET_Mode()
            self.HOME_Mode()

    def connectPLC(self):
        try:
            self.plc.connect(ip, rack, slot)
            if self.plc.get_connected():
                print("Connected to PLC Successfully!")
                self.plc_connected = True
                self.connected.emit(True)
            else:
                print("Connect PLC Failed!")
                self.connected.emit(False)
        except Exception as e:
            print(f"Connect PLC Failed! Error: {e}")
            self.error.emit(f"Connect PLC Failed! Error: {e}")
            self.label_error.setText(f"Connect PLC Failed! Error: {e}")

    def disconnectPLC(self):
        try:
            if self.plc.get_connected():
                self.stop()
                self.plc.disconnect()
                print("Disconnected from PLC successfully.")
                self.plc_connected = False
                self.connected.emit(False)
            else:
                print("PLC is already disconnected.")
                self.connected.emit(False)
        except Exception as e:
            print(f"Disconnect PLC Failed! Error: {e}")
            self.error.emit(f"Disconnect PLC Failed! Error: {e}")
            self.label_error.setText(f"Disconnect PLC Failed! Error: {e}")
    
    def read_plc_data(self):
        while self.plc_connected:
            try:
                self.plc_lock.acquire()
                delta_x = get_real(plc.db_read(1, 52, 4), 0)
                delta_y = get_real(plc.db_read(1, 56, 4), 0)
                delta_z = get_real(plc.db_read(1, 60, 4), 0)
                axis_x = get_real(plc.db_read(1, 64, 4), 0)
                axis_y = get_real(plc.db_read(1, 68, 4), 0)
                axis_z = get_real(plc.db_read(1, 72, 4), 0)
                tbTocdoBT = get_real(plc.db_read(1, 76, 4), 0)
                tbTocdoRB = get_real(plc.db_read(1, 80, 4), 0)
                self.update_plc_data_signal.emit(delta_x, delta_y, delta_z, axis_x, axis_y, axis_z, tbTocdoBT, tbTocdoRB)
                self.plc_lock.release()
                time.sleep(0.1)
            except Exception as e:
                self.plc_lock.release()
                print(f"Error reading PLC data: {e}")
                self.error.emit(f"Error reading PLC data: {e}")
                self.label_error.setText(f"Error reading PLC data: {e}")
    
    def START_Mode(self):
        try:
            if self.plc and self.plc.get_connected():
                db_number = 1
                byte_offset = 18
                bit_position = 1

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            self.system_active.setStyleSheet("background-color: green; color: white; font-size: 16px;")
                            self.system_active.setText("System: ACTIVE")
                            print("Start signal sent. Bit 18.1 is now ON.")
                        else:
                            print("Bit 18.1 is already ON. No action taken.")

                        break

                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(0.1)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
                self.label_error.setText("PLC is not connected. Please check the connection.")

        except Exception as e:
            print(f"Error sending start signal: {e}")
            self.error.emit(f"Error sending start signal: {e}")
            self.label_error.setText(f"Error sending start signal: {e}")
    
    def STOP_Mode(self):
        try:
            if self.plc and self.plc.get_connected():
                db_number = 1
                byte_offset = 18
                bit_position = 2 

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            self.system_active.setStyleSheet("background-color: red; color: white; font-size: 16px;")
                            self.system_active.setText("System: INACTIVE")
                            print("Stop signal sent. Bit 18.2 is now ON.")
                        else:
                            print("Bit 18.2 is already ON. No action taken.")
                        break
                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(0.1)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
                self.label_error.setText("PLC is not connected. Please check the connection.")
        except Exception as e:
            print(f"Error sending stop signal: {e}")
            self.error.emit(f"Error sending stop signal: {e}")
            self.label_error.setText(f"Error sending stop signal: {e}")

    def RESET_Mode(self):
        try:
            if self.plc and self.plc.get_connected():
                db_number = 1
                byte_offset = 18
                bit_position = 3 

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Reset signal sent. Bit 18.3 is now ON.")
                        else:
                            print("Bit 18.3 is already ON. No action taken.")
                        break
                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(0.1)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
                self.label_error.setText("PLC is not connected. Please check the connection.")
        except Exception as e:
            print(f"Error sending reset signal: {e}")
            self.error.emit(f"Error sending reset signal: {e}")
            self.label_error.setText(f"Error sending reset signal: {e}")

    def HOME_Mode(self):
        try:
            if self.plc and self.plc.get_connected():
                db_number = 1
                byte_offset = 18
                bit_position = 4

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Home signal sent. Bit 18.4 is now ON.")
                        else:
                            print("Bit 18.4 is already ON. No action taken.")
                        break
                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(0.1)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
                self.label_error.setText("PLC is not connected. Please check the connection.")
        except Exception as e:
            print(f"Error sending home signal: {e}")
            self.error.emit(f"Error sending home signal: {e}")
            self.label_error.setText(f"Error sending home signal: {e}")

    def display_first_box(self):
        if self.cameration_thread.update_first_box_data:
            print(f"First box center coordinates: {self.cameration_thread.update_first_box_data}")
            time.sleep(0.1)
        else:
            print("No box data available.")

    
    def stop(self):
        self.Running = False

class MainWindow(QMainWindow):

    def __init__(self):
        super().__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("Vision System")
        self.setWindowIcon(QIcon(":/icon/eye.png"))

        global deviceList
        deviceList = MV_CC_DEVICE_INFO_LIST()
        global cam
        cam = MvCamera()
        global nSelCamIndex
        nSelCamIndex = 0
        global obj_cam_operation
        obj_cam_operation = 0
        global isOpen
        isOpen = False
        global isGrabbing
        isGrabbing = False
        global isCalibMode 
        isCalibMode = True
        global checkin_programPLC 
        checkin_programPLC = False

        self.plc_controller = PLC_Thread(self)
        self.camera_thread = CameraOperation(cam, deviceList, parent=None)

        self.ui.slide_menu_container.hide()
        self.ui.stackedWidget.setCurrentIndex(0)
        self.ui.btn_menu.setChecked(True)
        # display_first_box()
        # setup_connections()
        self.ui.btn_Home.clicked.connect(self.on_btn_Home_clicked)
        self.ui.btn_Manual.clicked.connect(self.on_btn_Manual_clicked)
        self.ui.btn_Setting.clicked.connect(self.on_btn_Setting_clicked)
        self.ui.btn_User.clicked.connect(self.on_btn_User_clicked)
        self.ui.btn_exit.clicked.connect(self.closeEvent)
        self.ui.btn_menu.clicked.connect(self.on_btn_menu_clicked)
        self.ui.btn_Scan.clicked.connect(self.enum_devices)
        self.ui.bnOpen.clicked.connect(self.open_device)
        self.ui.bnClose.clicked.connect(self.close_device)
        self.ui.btn_ConnectCamera.clicked.connect(self.ConnectCamera)
        self.ui.bnStop.clicked.connect(self.stop_grabbing)
        # ui.btn_RUN_Program.clicked.connect(Start_Program)
        # ui.btn_Stop_Program.clicked.connect(Stop_Program)
        self.plc_controller.connected.connect(self.on_plc_connected)
        self.ui.btn_ConnectPLC.clicked.connect(self.start_plc_thread)
        self.ui.btn_START.clicked.connect(self.plc_controller.START_Mode)
        self.ui.btn_START_2.clicked.connect(self.plc_controller.START_Mode)
        self.ui.btn_STOP.clicked.connect(self.plc_controller.STOP_Mode)
        self.ui.btn_STOP_2.clicked.connect(self.plc_controller.STOP_Mode)
        self.ui.btn_RESET.clicked.connect(self.plc_controller.RESET_Mode)
        self.ui.btn_RESET_2.clicked.connect(self.plc_controller.RESET_Mode)
        self.ui.btn_HOME.clicked.connect(self.plc_controller.HOME_Mode)
        self.ui.btn_HOME_2.clicked.connect(self.plc_controller.HOME_Mode)

        self.ui.bnSoftwareTrigger.clicked.connect(self.trigger_once)
        self.ui.radioTriggerMode.clicked.connect(self.set_software_trigger_mode)
        self.ui.radioContinueMode.clicked.connect(self.set_continue_mode)

        self.ui.bnGetParam.clicked.connect(self.get_param)
        self.ui.bnSetParam.clicked.connect(self.set_param)
        self.ui.bnSaveImage.clicked.connect(self.save_bmp)
    
    def xFunc(self, event):
        global nSelCamIndex
        nSelCamIndex = TxtWrapBy("[", "]", self.ui.ComboDevices.get())

    def decoding_char(self, c_ubyte_value):
        c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
        try:
            decode_str = c_char_p_value.value.decode('gbk') 
        except UnicodeDecodeError:
            decode_str = str(c_char_p_value.value)
        return decode_str
    
    def enum_devices(self):
        global deviceList
        global obj_cam_operation

        deviceList = MV_CC_DEVICE_INFO_LIST()
        ret = MvCamera.MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, deviceList)
        if ret != 0:
            strError = "Enum devices fail! ret = :" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(mainWindow, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print("Find %d devices!" % deviceList.nDeviceNum)

        devList = []
        for i in range(0, deviceList.nDeviceNum):
            mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
            if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE:
                print("\ngige device: [%d]" % i)
                user_defined_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                model_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                print("device user define name: " + user_defined_name)
                print("device model name: " + model_name)

                nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))
                self.ui.lb_ipcamera.setText(str(nip1) + "." + str(nip2) + "." + str(nip3) + "." + str(nip4))
                devList.append(
                    "[" + str(i) + "]GigE: " + user_defined_name + " " + model_name + "(" + str(nip1) + "." + str(
                        nip2) + "." + str(nip3) + "." + str(nip4) + ")")
            elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                print("\nu3v device: [%d]" % i)
                user_defined_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                model_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                print("device user define name: " + user_defined_name)
                print("device model name: " + model_name)

                strSerialNumber = ""
                for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                    if per == 0:
                        break
                    strSerialNumber = strSerialNumber + chr(per)
                print("user serial number: " + strSerialNumber)
                devList.append("[" + str(i) + "]USB: " + user_defined_name + " " + model_name
                               + "(" + str(strSerialNumber) + ")")

        self.ui.ComboDevices.clear()
        self.ui.ComboDevices.addItems(devList)
        self.ui.ComboDevices.setCurrentIndex(0)
    
    def open_device(self):
        global deviceList
        global nSelCamIndex
        global obj_cam_operation
        global isOpen

        if isOpen:
            QMessageBox.warning(mainWindow, "Error", 'Camera is Running!', QMessageBox.Ok)
            return MV_E_CALLORDER

        nSelCamIndex = self.ui.ComboDevices.currentIndex()
        if nSelCamIndex < 0:
            QMessageBox.warning(mainWindow, "Error", 'Please select a camera!', QMessageBox.Ok)
            return MV_E_CALLORDER

        obj_cam_operation = CameraOperation(cam, deviceList, nSelCamIndex)
        ret = obj_cam_operation.Open_device()
        if 0 != ret:
            strError = "Open device failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
            isOpen = False
        else:
            self.set_continue_mode()
            self.get_param()
            isOpen = True
            self.ui.label_text.setText("Open device successfully!")
            self.enable_controls()
    
    def start_grabbing(self):
        global obj_cam_operation
        global isGrabbing

        ret = obj_cam_operation.Start_grabbing(self.ui.widgetDisplay, self.ui.tableWidget_out)
        if ret != 0:
            strError = "Start grabbing failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            isGrabbing = True
            self.ui.label_text.setText("Start grabbing successfully!")
            self.enable_controls()
    
    def ConnectCamera(self):
        global isGrabbing 
        isGrabbing = True 

        print ("Start all")
        self.enum_devices()
        self.open_device()
        self.start_grabbing()
    
    def stop_grabbing(self):
        global obj_cam_operation
        global isGrabbing

        ret = obj_cam_operation.Stop_grabbing()
        if ret != 0:
            strError = "Stop grabbing failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            isGrabbing = False
            self.ui.label_text.setText("Stop grabbing successfully!")
            self.enable_controls()
    
    def close_device(self):
        global isOpen
        global isGrabbing
        global obj_cam_operation

        if isOpen:
            obj_cam_operation.Close_device()
            isOpen = False

        isGrabbing = False
        self.enable_controls()
    
    def set_continue_mode(self):
        strError = None
        trigger_mode_enabled = False

        ret = obj_cam_operation.Set_trigger_mode(False)
        if ret != 0:
            strError = "Set continue mode failed ret:" + ToHexStr(ret) + " mode is " + str(trigger_mode_enabled)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            self.ui.radioContinueMode.setChecked(True)
            self.ui.radioTriggerMode.setChecked(False)
            self.ui.bnSoftwareTrigger.setEnabled(False)
    
    def set_software_trigger_mode(self):

        ret = obj_cam_operation.Set_trigger_mode(True)
        if ret != 0:
            strError = "Set trigger mode failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            self.ui.radioContinueMode.setChecked(False)
            self.ui.radioTriggerMode.setChecked(True)
            self.ui.bnSoftwareTrigger.setEnabled(isGrabbing)

    def trigger_once(self):
        ret = obj_cam_operation.Trigger_once()
        if ret != 0:
            strError = "TriggerSoftware failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
    
    def save_bmp(self):
        ret = obj_cam_operation.Save_Bmp()
        if ret != MV_OK:
            strError = "Save BMP failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            print("Save image success")
            
    def is_float(str):
        try:
            float(str)
            return True
        except ValueError:
            return False
    
    def get_param(self):
        ret = obj_cam_operation.Get_parameter()
        if ret != MV_OK:
            strError = "Get param failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            self.ui.edtExposureTime.setText("{0:.2f}".format(obj_cam_operation.exposure_time))
            self.ui.edtGain.setText("{0:.2f}".format(obj_cam_operation.gain))
            self.ui.edtFrameRate.setText("{0:.2f}".format(obj_cam_operation.frame_rate))

    def set_param(self):
        frame_rate = self.ui.edtFrameRate.text()
        exposure = self.ui.edtExposureTime.text()
        gain = self.ui.edtGain.text()

        if self.is_float(frame_rate)!=True or self.is_float(exposure)!=True or self.is_float(gain)!=True:
            strError = "Set param failed ret:" + ToHexStr(MV_E_PARAMETER)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
            return MV_E_PARAMETER
        
        ret = obj_cam_operation.Set_parameter(frame_rate, exposure, gain)
        if ret != MV_OK:
            strError = "Set param failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)

        return MV_OK
    
    def enable_controls(self):
        global isGrabbing
        global isOpen

        self.ui.bnOpen.setEnabled(not isOpen)
        self.ui.bnClose.setEnabled(isOpen)
        self.ui.btn_ConnectCamera.setEnabled(isOpen and (not isGrabbing))
        self.ui.bnStop.setEnabled(isOpen and isGrabbing)
        self.ui.bnSoftwareTrigger.setEnabled(isGrabbing and self.ui.radioTriggerMode.isChecked())
        self.ui.bnSaveImage.setEnabled(isOpen and isGrabbing)
    
    def display_first_box(self):
        self.camera_thread.update_coordinates.connect(self.hihi)
    
    def hihi(self, center_x, center_y):
        self.ui.Poy_X.setText(f"Center X: {center_x}")
        self.ui.Poy_Y.setText(f"Center Y: {center_y}")
        print(f"Nhận tín hiệu tọa độ: ({center_x}, {center_y})")
    
    def on_btn_menu_clicked(self, checked):
        if checked:
            self.ui.slide_menu_container.show()
        else:
            self.ui.slide_menu_container.hide()
    def on_btn_Home_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(0)
    def on_btn_Manual_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(1)
    def on_btn_Setting_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(2)
    def on_btn_User_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(3)
    
    # def on_btn_exit_clicked():
    #     plc_controller.disconnectPLC()
    #     close_device()
    #     sys.exit()
    #     print("Exit Program")
    
    def closeEvent(self):

        self.plc_controller.disconnectPLC()
        self.close_device()
        sys.exit()
        print("Exit Program")

    
    def start_plc_thread(self):
        self.plc_controller.update_plc_data_signal.connect(self.update_plc_data)
        self.plc_controller.start()
    def on_plc_connected(self, connected):
        if connected:
            self.ui.label_8.setText("PLC Siemens S7-1200")
            self.ui.label_11.setText(ip)
            self.ui.label_12.setText("Connected to PLC Successfully!")
        else:
            QMessageBox.warning(mainWindow,"PLC Connection", "Failed to connect to PLC.")
            self.ui.label_12.setText("Failed to connect to PLC.")
    def update_plc_data(self, delta_x, delta_y, delta_z, axis_x, axis_y, axis_z, tbTocdoBT, tbTocdoRB):
        self.ui.tbThetaX.setText(f"{delta_x:.2f}")
        self.ui.tbThetaY.setText(f"{delta_y:.2f}")
        self.ui.tbThetaZ.setText(f"{delta_z:.2f}")
        self.ui.tbAxisX.setText(f"{axis_x:.2f}")
        self.ui.tbAxisY.setText(f"{axis_y:.2f}")
        self.ui.tbAxisZ.setText(f"{axis_z:.2f}")
        self.ui.lTocdoBT.setText(f"{tbTocdoBT:.2f}")
        self.ui.lTocdoRB.setText(f"{tbTocdoRB:.2f}")
        
if __name__ == '__main__':
    app = QApplication(sys.argv)
    mainWindow = MainWindow()
    mainWindow.show()
    sys.exit(app.exec_())