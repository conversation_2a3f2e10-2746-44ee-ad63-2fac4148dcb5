# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'form.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1400, 900)
        MainWindow.setMinimumSize(QtCore.QSize(0, 0))
        MainWindow.setMaximumSize(QtCore.QSize(1400, 900))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        MainWindow.setFont(font)
        MainWindow.setStyleSheet("")
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setStyleSheet("background-color: rgb(24, 24, 36);")
        self.centralwidget.setObjectName("centralwidget")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.centralwidget)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.slide_menu_container = QtWidgets.QFrame(self.centralwidget)
        self.slide_menu_container.setMaximumSize(QtCore.QSize(200, 16777215))
        self.slide_menu_container.setStyleSheet("")
        self.slide_menu_container.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.slide_menu_container.setFrameShadow(QtWidgets.QFrame.Raised)
        self.slide_menu_container.setObjectName("slide_menu_container")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.slide_menu_container)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.frame_2 = QtWidgets.QFrame(self.slide_menu_container)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_5.setSpacing(0)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.logo_main = QtWidgets.QLabel(self.frame_2)
        font = QtGui.QFont()
        font.setPointSize(18)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        font.setStyleStrategy(QtGui.QFont.PreferAntialias)
        self.logo_main.setFont(font)
        self.logo_main.setStyleSheet("color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 rgba(0, 0, 0, 255), stop:0.33 rgba(0, 0, 0, 255), stop:0.34 rgba(255, 30, 30, 255), stop:0.66 rgba(255, 0, 0, 255), stop:0.67 rgba(255, 255, 0, 255), stop:1 rgba(255, 255, 0, 255));")
        self.logo_main.setObjectName("logo_main")
        self.verticalLayout_5.addWidget(self.logo_main)
        self.verticalLayout.addWidget(self.frame_2, 0, QtCore.Qt.AlignTop)
        self.frame_3 = QtWidgets.QFrame(self.slide_menu_container)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_3.sizePolicy().hasHeightForWidth())
        self.frame_3.setSizePolicy(sizePolicy)
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.frame_3)
        self.verticalLayout_6.setContentsMargins(0, 10, 0, 0)
        self.verticalLayout_6.setSpacing(5)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.btn_Home = QtWidgets.QPushButton(self.frame_3)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.btn_Home.setFont(font)
        self.btn_Home.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.btn_Home.setStyleSheet("#btn_Home {\n"
"    padding-left: 10px;\n"
"    text-align: left;\n"
"    color: rgb(85, 255, 255);\n"
"}\n"
"#btn_Home:hover{\n"
"    background-color: rgb(66, 66, 99);\n"
"}\n"
"#btn_Home:pressed{\n"
"    border-color: rgb(66, 66, 99);\n"
"}\n"
"#btn_Home:active {\n"
"    background-color: rgb(24, 24, 36);\n"
"}\n"
"#btn_Home:focus {\n"
"    background-color: rgb(66, 66, 99); /* Khôi phục màu mặc định sau khi nhấn */\n"
"    outline: none; /* Xóa viền focus */\n"
"}")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/Icons/Icons/home.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_Home.setIcon(icon)
        self.btn_Home.setIconSize(QtCore.QSize(40, 40))
        self.btn_Home.setCheckable(True)
        self.btn_Home.setAutoExclusive(True)
        self.btn_Home.setAutoRepeatDelay(300)
        self.btn_Home.setObjectName("btn_Home")
        self.verticalLayout_6.addWidget(self.btn_Home)
        self.btn_Manual = QtWidgets.QPushButton(self.frame_3)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.btn_Manual.setFont(font)
        self.btn_Manual.setStyleSheet("#btn_Manual {\n"
"    padding-left: 10px;\n"
"    text-align: left;\n"
"    color: rgb(85, 255, 255);\n"
"}\n"
"#btn_Manual:hover{\n"
"    background-color: rgb(66, 66, 99);\n"
"}\n"
"#btn_Manual:pressed{\n"
"    border-color: rgb(66, 66, 99);\n"
"}\n"
"#btn_Manual:active {\n"
"    background-color: rgb(24, 24, 36);\n"
"}\n"
"#btn_Manual:focus {\n"
"    background-color: rgb(66, 66, 99); /* Khôi phục màu mặc định sau khi nhấn */\n"
"    outline: none; /* Xóa viền focus */\n"
"}")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/Icons/Icons/move.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_Manual.setIcon(icon1)
        self.btn_Manual.setIconSize(QtCore.QSize(40, 40))
        self.btn_Manual.setCheckable(True)
        self.btn_Manual.setAutoExclusive(True)
        self.btn_Manual.setObjectName("btn_Manual")
        self.verticalLayout_6.addWidget(self.btn_Manual)
        self.btn_Setting = QtWidgets.QPushButton(self.frame_3)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.btn_Setting.setFont(font)
        self.btn_Setting.setStyleSheet("#btn_Setting {\n"
"    padding-left: 10px;\n"
"    text-align: left;\n"
"    color: rgb(85, 255, 255);\n"
"} \n"
"#btn_Setting:hover{\n"
"    background-color: rgb(66, 66, 99);\n"
"}\n"
"#btn_Setting:pressed{\n"
"    border-color: rgb(66, 66, 99);\n"
"}\n"
"#btn_Setting:active {\n"
"    background-color: rgb(24, 24, 36);\n"
"}\n"
"#btn_Setting:focus {\n"
"    background-color: rgb(66, 66, 99); /* Khôi phục màu mặc định sau khi nhấn */\n"
"    outline: none; /* Xóa viền focus */\n"
"}")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/Icons/Icons/settings.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_Setting.setIcon(icon2)
        self.btn_Setting.setIconSize(QtCore.QSize(40, 40))
        self.btn_Setting.setCheckable(True)
        self.btn_Setting.setAutoExclusive(True)
        self.btn_Setting.setObjectName("btn_Setting")
        self.verticalLayout_6.addWidget(self.btn_Setting)
        self.btn_User = QtWidgets.QPushButton(self.frame_3)
        self.btn_User.setStyleSheet("#btn_User {\n"
"    padding-left: 10px;\n"
"    text-align: left;\n"
"    color: rgb(85, 255, 255);\n"
"    background-color: rgb(24, 24, 36);\n"
"}\n"
"#btn_User:hover{\n"
"    background-color: rgb(66, 66, 99);\n"
"}\n"
"#btn_User:pressed{\n"
"    border-color: rgb(66, 66, 99);\n"
"}\n"
"#btn_User:active {\n"
"    background-color: rgb(24, 24, 36);\n"
"}\n"
"#btn_User:focus {\n"
"    background-color: rgb(66, 66, 99); /* Khôi phục màu mặc định sau khi nhấn */\n"
"    outline: none; /* Xóa viền focus */\n"
"}\n"
"\n"
"")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/Icons/Icons/users.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_User.setIcon(icon3)
        self.btn_User.setIconSize(QtCore.QSize(40, 40))
        self.btn_User.setCheckable(True)
        self.btn_User.setAutoExclusive(True)
        self.btn_User.setObjectName("btn_User")
        self.verticalLayout_6.addWidget(self.btn_User)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_6.addItem(spacerItem)
        self.verticalLayout.addWidget(self.frame_3)
        self.frame_4 = QtWidgets.QFrame(self.slide_menu_container)
        self.frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.frame_4)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.btn_exit = QtWidgets.QPushButton(self.frame_4)
        self.btn_exit.setStyleSheet("color: rgb(170, 255, 255);")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/Icons/Icons/power.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_exit.setIcon(icon4)
        self.btn_exit.setIconSize(QtCore.QSize(40, 40))
        self.btn_exit.setCheckable(True)
        self.btn_exit.setAutoExclusive(True)
        self.btn_exit.setObjectName("btn_exit")
        self.horizontalLayout_8.addWidget(self.btn_exit, 0, QtCore.Qt.AlignBottom)
        self.verticalLayout.addWidget(self.frame_4, 0, QtCore.Qt.AlignBottom)
        self.horizontalLayout.addWidget(self.slide_menu_container)
        self.main_body = QtWidgets.QFrame(self.centralwidget)
        self.main_body.setStyleSheet("")
        self.main_body.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.main_body.setFrameShadow(QtWidgets.QFrame.Raised)
        self.main_body.setObjectName("main_body")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.main_body)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.header_frame = QtWidgets.QFrame(self.main_body)
        self.header_frame.setStyleSheet("color: rgb(170, 255, 255);")
        self.header_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.header_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.header_frame.setObjectName("header_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.header_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.frame_7 = QtWidgets.QFrame(self.header_frame)
        self.frame_7.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_7.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_7.setObjectName("frame_7")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.frame_7)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.frame = QtWidgets.QFrame(self.frame_7)
        self.frame.setMinimumSize(QtCore.QSize(65, 0))
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.btn_menu = QtWidgets.QPushButton(self.frame)
        self.btn_menu.setText("")
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(":/Icons/Icons/align-left.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_menu.setIcon(icon5)
        self.btn_menu.setIconSize(QtCore.QSize(32, 32))
        self.btn_menu.setCheckable(True)
        self.btn_menu.setAutoExclusive(True)
        self.btn_menu.setObjectName("btn_menu")
        self.horizontalLayout_7.addWidget(self.btn_menu, 0, QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.horizontalLayout_6.addWidget(self.frame)
        self.search_lineEdit = QtWidgets.QLineEdit(self.frame_7)
        self.search_lineEdit.setMinimumSize(QtCore.QSize(500, 32))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.search_lineEdit.setFont(font)
        self.search_lineEdit.setStyleSheet("color: rgb(85, 255, 255);\n"
"background-color: rgb(24, 24, 36);")
        self.search_lineEdit.setText("")
        self.search_lineEdit.setObjectName("search_lineEdit")
        self.horizontalLayout_6.addWidget(self.search_lineEdit, 0, QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.btn_search = QtWidgets.QPushButton(self.frame_7)
        self.btn_search.setMinimumSize(QtCore.QSize(30, 0))
        self.btn_search.setText("")
        icon6 = QtGui.QIcon()
        icon6.addPixmap(QtGui.QPixmap(":/Icons/Icons/search.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_search.setIcon(icon6)
        self.btn_search.setIconSize(QtCore.QSize(32, 32))
        self.btn_search.setObjectName("btn_search")
        self.horizontalLayout_6.addWidget(self.btn_search)
        self.horizontalLayout_2.addWidget(self.frame_7)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem1)
        self.frame_8 = QtWidgets.QFrame(self.header_frame)
        self.frame_8.setMinimumSize(QtCore.QSize(60, 0))
        self.frame_8.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_8.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_8.setObjectName("frame_8")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.frame_8)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.btn_admin = QtWidgets.QPushButton(self.frame_8)
        self.btn_admin.setText("")
        icon7 = QtGui.QIcon()
        icon7.addPixmap(QtGui.QPixmap(":/Icons/Icons/user.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_admin.setIcon(icon7)
        self.btn_admin.setIconSize(QtCore.QSize(32, 32))
        self.btn_admin.setCheckable(True)
        self.btn_admin.setAutoExclusive(True)
        self.btn_admin.setObjectName("btn_admin")
        self.horizontalLayout_5.addWidget(self.btn_admin, 0, QtCore.Qt.AlignBottom)
        self.btn_Notification = QtWidgets.QPushButton(self.frame_8)
        self.btn_Notification.setText("")
        icon8 = QtGui.QIcon()
        icon8.addPixmap(QtGui.QPixmap(":/Icons/Icons/bell.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_Notification.setIcon(icon8)
        self.btn_Notification.setIconSize(QtCore.QSize(32, 32))
        self.btn_Notification.setCheckable(True)
        self.btn_Notification.setAutoExclusive(True)
        self.btn_Notification.setObjectName("btn_Notification")
        self.horizontalLayout_5.addWidget(self.btn_Notification, 0, QtCore.Qt.AlignBottom)
        self.horizontalLayout_2.addWidget(self.frame_8, 0, QtCore.Qt.AlignRight|QtCore.Qt.AlignTop)
        self.verticalLayout_2.addWidget(self.header_frame, 0, QtCore.Qt.AlignTop)
        self.main_body_contents = QtWidgets.QFrame(self.main_body)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.main_body_contents.sizePolicy().hasHeightForWidth())
        self.main_body_contents.setSizePolicy(sizePolicy)
        self.main_body_contents.setMinimumSize(QtCore.QSize(645, 519))
        self.main_body_contents.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.main_body_contents.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.main_body_contents.setFrameShadow(QtWidgets.QFrame.Raised)
        self.main_body_contents.setObjectName("main_body_contents")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.main_body_contents)
        self.verticalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_7.setSpacing(0)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.stackedWidget = QtWidgets.QStackedWidget(self.main_body_contents)
        self.stackedWidget.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.stackedWidget.setObjectName("stackedWidget")
        self.page = QtWidgets.QWidget()
        self.page.setObjectName("page")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.page)
        self.verticalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_8.setSpacing(0)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.frame_5 = QtWidgets.QFrame(self.page)
        self.frame_5.setStyleSheet(".frame-gradient {\n"
"  border: 5px solid;\n"
"  border-image: linear-gradient(to right, #ff7e5f, #feb47b) 1; /* Viền gradient */\n"
"}")
        self.frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_5.setObjectName("frame_5")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.frame_5)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.frame_15 = QtWidgets.QFrame(self.frame_5)
        self.frame_15.setStyleSheet("")
        self.frame_15.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_15.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_15.setObjectName("frame_15")
        self.widgetDisplay = QtWidgets.QLabel(self.frame_15)
        self.widgetDisplay.setGeometry(QtCore.QRect(0, 50, 640, 480))
        self.widgetDisplay.setText("")
        self.widgetDisplay.setObjectName("widgetDisplay")
        self.tableWidget_out = QtWidgets.QTableWidget(self.frame_15)
        self.tableWidget_out.setGeometry(QtCore.QRect(0, 560, 640, 220))
        self.tableWidget_out.setStyleSheet("background-color: rgb(214, 214, 214);")
        self.tableWidget_out.setObjectName("tableWidget_out")
        self.tableWidget_out.setColumnCount(4)
        self.tableWidget_out.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.tableWidget_out.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.tableWidget_out.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.tableWidget_out.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.tableWidget_out.setHorizontalHeaderItem(3, item)
        self.label_15 = QtWidgets.QLabel(self.frame_15)
        self.label_15.setGeometry(QtCore.QRect(0, 0, 200, 40))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.label_15.setFont(font)
        self.label_15.setStyleSheet("color: rgb(85, 255, 255);")
        self.label_15.setObjectName("label_15")
        self.horizontalLayout_9.addWidget(self.frame_15)
        self.frame_12 = QtWidgets.QFrame(self.frame_5)
        self.frame_12.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_12.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_12.setObjectName("frame_12")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.frame_12)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setSpacing(0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.Gbox_ConnectDeltaRobot = QtWidgets.QGroupBox(self.frame_12)
        self.Gbox_ConnectDeltaRobot.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.Gbox_ConnectDeltaRobot.setFont(font)
        self.Gbox_ConnectDeltaRobot.setStyleSheet("#Gbox_ConnectDeltaRobot{\n"
"    color: rgb(85, 255, 255);\n"
"}")
        self.Gbox_ConnectDeltaRobot.setObjectName("Gbox_ConnectDeltaRobot")
        self.btn_START = QtWidgets.QPushButton(self.Gbox_ConnectDeltaRobot)
        self.btn_START.setGeometry(QtCore.QRect(70, 650, 200, 50))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.btn_START.setFont(font)
        self.btn_START.setStyleSheet("background-color: rgb(85, 255, 127);")
        self.btn_START.setObjectName("btn_START")
        self.btn_STOP = QtWidgets.QPushButton(self.Gbox_ConnectDeltaRobot)
        self.btn_STOP.setGeometry(QtCore.QRect(70, 720, 200, 50))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.btn_STOP.setFont(font)
        self.btn_STOP.setStyleSheet("background-color: rgb(255, 0, 0);")
        self.btn_STOP.setObjectName("btn_STOP")
        self.label = QtWidgets.QLabel(self.Gbox_ConnectDeltaRobot)
        self.label.setGeometry(QtCore.QRect(10, 30, 51, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setStyleSheet("color: rgb(85, 255, 255);")
        self.label.setObjectName("label")
        self.btn_ConnectPLC = QtWidgets.QPushButton(self.Gbox_ConnectDeltaRobot)
        self.btn_ConnectPLC.setGeometry(QtCore.QRect(20, 210, 560, 40))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.btn_ConnectPLC.setFont(font)
        self.btn_ConnectPLC.setStyleSheet("color: rgb(85, 255, 255);\n"
"background-color: rgb(75, 75, 75);")
        self.btn_ConnectPLC.setCheckable(False)
        self.btn_ConnectPLC.setObjectName("btn_ConnectPLC")
        self.label_2 = QtWidgets.QLabel(self.Gbox_ConnectDeltaRobot)
        self.label_2.setGeometry(QtCore.QRect(10, 260, 121, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label_2.setFont(font)
        self.label_2.setStyleSheet("color: rgb(85, 255, 255);")
        self.label_2.setObjectName("label_2")
        self.btn_ConnectCamera = QtWidgets.QPushButton(self.Gbox_ConnectDeltaRobot)
        self.btn_ConnectCamera.setGeometry(QtCore.QRect(20, 590, 560, 40))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.btn_ConnectCamera.setFont(font)
        self.btn_ConnectCamera.setStyleSheet("color: rgb(85, 255, 255);\n"
"background-color: rgb(75, 75, 75);")
        self.btn_ConnectCamera.setObjectName("btn_ConnectCamera")
        self.frame_13 = QtWidgets.QFrame(self.Gbox_ConnectDeltaRobot)
        self.frame_13.setGeometry(QtCore.QRect(20, 70, 560, 128))
        self.frame_13.setMaximumSize(QtCore.QSize(560, 128))
        self.frame_13.setStyleSheet("background-color: rgb(220, 220, 220);")
        self.frame_13.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_13.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_13.setObjectName("frame_13")
        self.label_5 = QtWidgets.QLabel(self.frame_13)
        self.label_5.setGeometry(QtCore.QRect(20, 10, 80, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_5.setFont(font)
        self.label_5.setObjectName("label_5")
        self.label_6 = QtWidgets.QLabel(self.frame_13)
        self.label_6.setGeometry(QtCore.QRect(20, 50, 80, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_6.setFont(font)
        self.label_6.setObjectName("label_6")
        self.label_7 = QtWidgets.QLabel(self.frame_13)
        self.label_7.setGeometry(QtCore.QRect(20, 90, 80, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_7.setFont(font)
        self.label_7.setObjectName("label_7")
        self.label_8 = QtWidgets.QLabel(self.frame_13)
        self.label_8.setGeometry(QtCore.QRect(100, 10, 360, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_8.setFont(font)
        self.label_8.setStyleSheet("color: rgb(255, 0, 0);\n"
"background-color: rgb(255, 255, 255);\n"
"")
        self.label_8.setText("")
        self.label_8.setObjectName("label_8")
        self.label_11 = QtWidgets.QLabel(self.frame_13)
        self.label_11.setGeometry(QtCore.QRect(100, 50, 360, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_11.setFont(font)
        self.label_11.setStyleSheet("color: rgb(255, 0, 0);\n"
"background-color: rgb(255, 255, 255);\n"
"")
        self.label_11.setText("")
        self.label_11.setObjectName("label_11")
        self.label_12 = QtWidgets.QLabel(self.frame_13)
        self.label_12.setGeometry(QtCore.QRect(100, 90, 360, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_12.setFont(font)
        self.label_12.setStyleSheet("color: rgb(255, 0, 0);\n"
"background-color: rgb(255, 255, 255);\n"
"")
        self.label_12.setText("")
        self.label_12.setObjectName("label_12")
        self.frame_14 = QtWidgets.QFrame(self.Gbox_ConnectDeltaRobot)
        self.frame_14.setGeometry(QtCore.QRect(20, 290, 560, 291))
        self.frame_14.setStyleSheet("background-color: rgb(220, 220, 220);")
        self.frame_14.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_14.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_14.setObjectName("frame_14")
        self.label_3 = QtWidgets.QLabel(self.frame_14)
        self.label_3.setGeometry(QtCore.QRect(20, 10, 80, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_3.setFont(font)
        self.label_3.setObjectName("label_3")
        self.label_10 = QtWidgets.QLabel(self.frame_14)
        self.label_10.setGeometry(QtCore.QRect(20, 50, 80, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_10.setFont(font)
        self.label_10.setObjectName("label_10")
        self.lb_ipcamera = QtWidgets.QLabel(self.frame_14)
        self.lb_ipcamera.setGeometry(QtCore.QRect(100, 50, 360, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.lb_ipcamera.setFont(font)
        self.lb_ipcamera.setStyleSheet("color: rgb(255, 0, 0);\n"
"background-color: rgb(255, 255, 255);\n"
"")
        self.lb_ipcamera.setText("")
        self.lb_ipcamera.setObjectName("lb_ipcamera")
        self.ComboDevices = QtWidgets.QComboBox(self.frame_14)
        self.ComboDevices.setGeometry(QtCore.QRect(100, 10, 360, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.ComboDevices.setFont(font)
        self.ComboDevices.setStyleSheet("color: rgb(255, 0, 0);")
        self.ComboDevices.setObjectName("ComboDevices")
        self.btn_Scan = QtWidgets.QPushButton(self.frame_14)
        self.btn_Scan.setGeometry(QtCore.QRect(465, 10, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.btn_Scan.setFont(font)
        self.btn_Scan.setStyleSheet("background-color: rgb(255, 85, 0);\n"
"color: rgb(255, 255, 255);")
        self.btn_Scan.setObjectName("btn_Scan")
        self.bnClose = QtWidgets.QPushButton(self.frame_14)
        self.bnClose.setGeometry(QtCore.QRect(150, 130, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.bnClose.setFont(font)
        self.bnClose.setStyleSheet("color: rgb(255, 255, 255);\n"
"background-color: rgb(255, 0, 0);")
        self.bnClose.setObjectName("bnClose")
        self.radioContinueMode = QtWidgets.QRadioButton(self.frame_14)
        self.radioContinueMode.setGeometry(QtCore.QRect(30, 170, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.radioContinueMode.setFont(font)
        self.radioContinueMode.setStyleSheet("")
        self.radioContinueMode.setObjectName("radioContinueMode")
        self.radioTriggerMode = QtWidgets.QRadioButton(self.frame_14)
        self.radioTriggerMode.setGeometry(QtCore.QRect(160, 170, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.radioTriggerMode.setFont(font)
        self.radioTriggerMode.setObjectName("radioTriggerMode")
        self.bnStop = QtWidgets.QPushButton(self.frame_14)
        self.bnStop.setEnabled(False)
        self.bnStop.setGeometry(QtCore.QRect(30, 250, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.bnStop.setFont(font)
        self.bnStop.setStyleSheet("background-color: rgb(255, 0, 0);\n"
"color: rgb(255, 255, 255);")
        self.bnStop.setObjectName("bnStop")
        self.bnOpen = QtWidgets.QPushButton(self.frame_14)
        self.bnOpen.setGeometry(QtCore.QRect(30, 130, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.bnOpen.setFont(font)
        self.bnOpen.setStyleSheet("background-color: rgb(85, 255, 127);\n"
"color: rgb(255, 0, 0);")
        self.bnOpen.setObjectName("bnOpen")
        self.bnSaveImage = QtWidgets.QPushButton(self.frame_14)
        self.bnSaveImage.setEnabled(False)
        self.bnSaveImage.setGeometry(QtCore.QRect(150, 250, 100, 28))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.bnSaveImage.setFont(font)
        self.bnSaveImage.setStyleSheet("background-color: rgb(170, 255, 255);\n"
"color: rgb(255, 0, 0);")
        self.bnSaveImage.setObjectName("bnSaveImage")
        self.bnSoftwareTrigger = QtWidgets.QPushButton(self.frame_14)
        self.bnSoftwareTrigger.setEnabled(False)
        self.bnSoftwareTrigger.setGeometry(QtCore.QRect(150, 210, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.bnSoftwareTrigger.setFont(font)
        self.bnSoftwareTrigger.setStyleSheet("color: rgb(0, 0, 0);")
        self.bnSoftwareTrigger.setObjectName("bnSoftwareTrigger")
        self.label_4 = QtWidgets.QLabel(self.frame_14)
        self.label_4.setGeometry(QtCore.QRect(290, 130, 70, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_4.setFont(font)
        self.label_4.setObjectName("label_4")
        self.edtExposureTime = QtWidgets.QLineEdit(self.frame_14)
        self.edtExposureTime.setGeometry(QtCore.QRect(420, 130, 140, 30))
        self.edtExposureTime.setObjectName("edtExposureTime")
        self.label_13 = QtWidgets.QLabel(self.frame_14)
        self.label_13.setGeometry(QtCore.QRect(290, 170, 70, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_13.setFont(font)
        self.label_13.setObjectName("label_13")
        self.edtGain = QtWidgets.QLineEdit(self.frame_14)
        self.edtGain.setGeometry(QtCore.QRect(420, 170, 140, 30))
        self.edtGain.setObjectName("edtGain")
        self.label_9 = QtWidgets.QLabel(self.frame_14)
        self.label_9.setGeometry(QtCore.QRect(290, 210, 81, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_9.setFont(font)
        self.label_9.setObjectName("label_9")
        self.edtFrameRate = QtWidgets.QLineEdit(self.frame_14)
        self.edtFrameRate.setGeometry(QtCore.QRect(420, 210, 140, 30))
        self.edtFrameRate.setObjectName("edtFrameRate")
        self.bnGetParam = QtWidgets.QPushButton(self.frame_14)
        self.bnGetParam.setGeometry(QtCore.QRect(290, 250, 121, 28))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.bnGetParam.setFont(font)
        self.bnGetParam.setObjectName("bnGetParam")
        self.bnSetParam = QtWidgets.QPushButton(self.frame_14)
        self.bnSetParam.setGeometry(QtCore.QRect(420, 250, 140, 28))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.bnSetParam.setFont(font)
        self.bnSetParam.setObjectName("bnSetParam")
        self.label_text = QtWidgets.QLabel(self.frame_14)
        self.label_text.setGeometry(QtCore.QRect(100, 90, 360, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_text.setFont(font)
        self.label_text.setStyleSheet("color: rgb(255, 0, 0);\n"
"background-color: rgb(255, 255, 255);\n"
"")
        self.label_text.setText("")
        self.label_text.setObjectName("label_text")
        self.label_14 = QtWidgets.QLabel(self.frame_14)
        self.label_14.setGeometry(QtCore.QRect(20, 90, 80, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.label_14.setFont(font)
        self.label_14.setObjectName("label_14")
        self.bnStart = QtWidgets.QPushButton(self.frame_14)
        self.bnStart.setGeometry(QtCore.QRect(30, 210, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        self.bnStart.setFont(font)
        self.bnStart.setStyleSheet("background-color: rgb(85, 170, 127);\n"
"color: rgb(255, 255, 255);")
        self.bnStart.setObjectName("bnStart")
        self.btn_RESET = QtWidgets.QPushButton(self.Gbox_ConnectDeltaRobot)
        self.btn_RESET.setGeometry(QtCore.QRect(340, 650, 200, 50))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.btn_RESET.setFont(font)
        self.btn_RESET.setStyleSheet("background-color: rgb(255, 255, 0);")
        self.btn_RESET.setObjectName("btn_RESET")
        self.btn_HOME = QtWidgets.QPushButton(self.Gbox_ConnectDeltaRobot)
        self.btn_HOME.setGeometry(QtCore.QRect(340, 720, 200, 50))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.btn_HOME.setFont(font)
        self.btn_HOME.setStyleSheet("background-color: rgb(170, 255, 255);")
        self.btn_HOME.setObjectName("btn_HOME")
        self.horizontalLayout_10.addWidget(self.Gbox_ConnectDeltaRobot)
        self.horizontalLayout_9.addWidget(self.frame_12)
        self.verticalLayout_8.addWidget(self.frame_5)
        self.stackedWidget.addWidget(self.page)
        self.page_1 = QtWidgets.QWidget()
        self.page_1.setObjectName("page_1")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.page_1)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.frame_9 = QtWidgets.QFrame(self.page_1)
        self.frame_9.setStyleSheet("background-color: rgb(24, 24, 36);")
        self.frame_9.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_9.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_9.setObjectName("frame_9")
        self.groupBox = QtWidgets.QGroupBox(self.frame_9)
        self.groupBox.setGeometry(QtCore.QRect(10, 70, 1170, 331))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(15)
        font.setBold(True)
        font.setItalic(True)
        font.setWeight(75)
        self.groupBox.setFont(font)
        self.groupBox.setStyleSheet("color: rgb(85, 255, 255);\n"
"background-color: rgb(24, 24, 36);")
        self.groupBox.setObjectName("groupBox")
        self.doubleSpinBox = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBox.setGeometry(QtCore.QRect(600, 230, 170, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.doubleSpinBox.setFont(font)
        self.doubleSpinBox.setAlignment(QtCore.Qt.AlignCenter)
        self.doubleSpinBox.setDecimals(2)
        self.doubleSpinBox.setMinimum(-99.0)
        self.doubleSpinBox.setSingleStep(1.0)
        self.doubleSpinBox.setObjectName("doubleSpinBox")
        self.label_22 = QtWidgets.QLabel(self.groupBox)
        self.label_22.setGeometry(QtCore.QRect(380, 230, 190, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_22.setFont(font)
        self.label_22.setObjectName("label_22")
        self.doubleSpinBox_2 = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBox_2.setGeometry(QtCore.QRect(600, 270, 170, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.doubleSpinBox_2.setFont(font)
        self.doubleSpinBox_2.setAlignment(QtCore.Qt.AlignCenter)
        self.doubleSpinBox_2.setMinimum(-99.0)
        self.doubleSpinBox_2.setObjectName("doubleSpinBox_2")
        self.label_23 = QtWidgets.QLabel(self.groupBox)
        self.label_23.setGeometry(QtCore.QRect(380, 270, 190, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_23.setFont(font)
        self.label_23.setObjectName("label_23")
        self.label_31 = QtWidgets.QLabel(self.groupBox)
        self.label_31.setGeometry(QtCore.QRect(380, 190, 220, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_31.setFont(font)
        self.label_31.setObjectName("label_31")
        self.label_SL = QtWidgets.QLineEdit(self.groupBox)
        self.label_SL.setGeometry(QtCore.QRect(600, 190, 170, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_SL.setFont(font)
        self.label_SL.setAlignment(QtCore.Qt.AlignCenter)
        self.label_SL.setObjectName("label_SL")
        self.label_32 = QtWidgets.QLabel(self.groupBox)
        self.label_32.setGeometry(QtCore.QRect(380, 40, 190, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_32.setFont(font)
        self.label_32.setObjectName("label_32")
        self.lTocdoRB = QtWidgets.QLineEdit(self.groupBox)
        self.lTocdoRB.setGeometry(QtCore.QRect(600, 40, 170, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.lTocdoRB.setFont(font)
        self.lTocdoRB.setAlignment(QtCore.Qt.AlignCenter)
        self.lTocdoRB.setObjectName("lTocdoRB")
        self.label_33 = QtWidgets.QLabel(self.groupBox)
        self.label_33.setGeometry(QtCore.QRect(380, 80, 190, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_33.setFont(font)
        self.label_33.setObjectName("label_33")
        self.lTocdoBT = QtWidgets.QLineEdit(self.groupBox)
        self.lTocdoBT.setGeometry(QtCore.QRect(600, 80, 170, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.lTocdoBT.setFont(font)
        self.lTocdoBT.setAlignment(QtCore.Qt.AlignCenter)
        self.lTocdoBT.setObjectName("lTocdoBT")
        self.label_loaiSP = QtWidgets.QLineEdit(self.groupBox)
        self.label_loaiSP.setGeometry(QtCore.QRect(600, 120, 170, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_loaiSP.setFont(font)
        self.label_loaiSP.setAlignment(QtCore.Qt.AlignCenter)
        self.label_loaiSP.setObjectName("label_loaiSP")
        self.label_34 = QtWidgets.QLabel(self.groupBox)
        self.label_34.setGeometry(QtCore.QRect(380, 120, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_34.setFont(font)
        self.label_34.setObjectName("label_34")
        self.label_29 = QtWidgets.QLabel(self.groupBox)
        self.label_29.setGeometry(QtCore.QRect(20, 40, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_29.setFont(font)
        self.label_29.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_29.setObjectName("label_29")
        self.label_28 = QtWidgets.QLabel(self.groupBox)
        self.label_28.setGeometry(QtCore.QRect(20, 80, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_28.setFont(font)
        self.label_28.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_28.setObjectName("label_28")
        self.label_27 = QtWidgets.QLabel(self.groupBox)
        self.label_27.setGeometry(QtCore.QRect(20, 120, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_27.setFont(font)
        self.label_27.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_27.setObjectName("label_27")
        self.label_24 = QtWidgets.QLabel(self.groupBox)
        self.label_24.setGeometry(QtCore.QRect(20, 190, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_24.setFont(font)
        self.label_24.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_24.setObjectName("label_24")
        self.label_25 = QtWidgets.QLabel(self.groupBox)
        self.label_25.setGeometry(QtCore.QRect(20, 230, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_25.setFont(font)
        self.label_25.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_25.setObjectName("label_25")
        self.label_26 = QtWidgets.QLabel(self.groupBox)
        self.label_26.setGeometry(QtCore.QRect(20, 270, 100, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_26.setFont(font)
        self.label_26.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_26.setObjectName("label_26")
        self.tbThetaX = QtWidgets.QLineEdit(self.groupBox)
        self.tbThetaX.setGeometry(QtCore.QRect(130, 40, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.tbThetaX.setFont(font)
        self.tbThetaX.setAlignment(QtCore.Qt.AlignCenter)
        self.tbThetaX.setReadOnly(True)
        self.tbThetaX.setObjectName("tbThetaX")
        self.tbThetaY = QtWidgets.QLineEdit(self.groupBox)
        self.tbThetaY.setGeometry(QtCore.QRect(130, 80, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.tbThetaY.setFont(font)
        self.tbThetaY.setAlignment(QtCore.Qt.AlignCenter)
        self.tbThetaY.setReadOnly(True)
        self.tbThetaY.setObjectName("tbThetaY")
        self.tbThetaZ = QtWidgets.QLineEdit(self.groupBox)
        self.tbThetaZ.setGeometry(QtCore.QRect(130, 120, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.tbThetaZ.setFont(font)
        self.tbThetaZ.setAlignment(QtCore.Qt.AlignCenter)
        self.tbThetaZ.setReadOnly(True)
        self.tbThetaZ.setObjectName("tbThetaZ")
        self.tbAxisX = QtWidgets.QLineEdit(self.groupBox)
        self.tbAxisX.setGeometry(QtCore.QRect(130, 190, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.tbAxisX.setFont(font)
        self.tbAxisX.setAlignment(QtCore.Qt.AlignCenter)
        self.tbAxisX.setReadOnly(True)
        self.tbAxisX.setObjectName("tbAxisX")
        self.tbAxisY = QtWidgets.QLineEdit(self.groupBox)
        self.tbAxisY.setGeometry(QtCore.QRect(130, 230, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.tbAxisY.setFont(font)
        self.tbAxisY.setAlignment(QtCore.Qt.AlignCenter)
        self.tbAxisY.setReadOnly(True)
        self.tbAxisY.setObjectName("tbAxisY")
        self.tbAxisZ = QtWidgets.QLineEdit(self.groupBox)
        self.tbAxisZ.setGeometry(QtCore.QRect(130, 270, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.tbAxisZ.setFont(font)
        self.tbAxisZ.setAlignment(QtCore.Qt.AlignCenter)
        self.tbAxisZ.setReadOnly(True)
        self.tbAxisZ.setObjectName("tbAxisZ")
        self.label_30 = QtWidgets.QLabel(self.groupBox)
        self.label_30.setGeometry(QtCore.QRect(830, 80, 60, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_30.setFont(font)
        self.label_30.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_30.setObjectName("label_30")
        self.label_35 = QtWidgets.QLabel(self.groupBox)
        self.label_35.setGeometry(QtCore.QRect(830, 40, 60, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_35.setFont(font)
        self.label_35.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_35.setObjectName("label_35")
        self.Pos_X = QtWidgets.QLineEdit(self.groupBox)
        self.Pos_X.setGeometry(QtCore.QRect(920, 40, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.Pos_X.setFont(font)
        self.Pos_X.setAlignment(QtCore.Qt.AlignCenter)
        self.Pos_X.setReadOnly(True)
        self.Pos_X.setObjectName("Pos_X")
        self.Pos_Y = QtWidgets.QLineEdit(self.groupBox)
        self.Pos_Y.setGeometry(QtCore.QRect(920, 80, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.Pos_Y.setFont(font)
        self.Pos_Y.setAlignment(QtCore.Qt.AlignCenter)
        self.Pos_Y.setReadOnly(True)
        self.Pos_Y.setObjectName("Pos_Y")
        self.label_42 = QtWidgets.QLabel(self.groupBox)
        self.label_42.setGeometry(QtCore.QRect(830, 190, 81, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_42.setFont(font)
        self.label_42.setObjectName("label_42")
        self.label_43 = QtWidgets.QLabel(self.groupBox)
        self.label_43.setGeometry(QtCore.QRect(830, 230, 81, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_43.setFont(font)
        self.label_43.setObjectName("label_43")
        self.label_qr2 = QtWidgets.QLineEdit(self.groupBox)
        self.label_qr2.setGeometry(QtCore.QRect(920, 190, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_qr2.setFont(font)
        self.label_qr2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_qr2.setReadOnly(True)
        self.label_qr2.setObjectName("label_qr2")
        self.label_qr3 = QtWidgets.QLineEdit(self.groupBox)
        self.label_qr3.setGeometry(QtCore.QRect(920, 230, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_qr3.setFont(font)
        self.label_qr3.setAlignment(QtCore.Qt.AlignCenter)
        self.label_qr3.setReadOnly(True)
        self.label_qr3.setObjectName("label_qr3")
        self.btn_HOME_2 = QtWidgets.QPushButton(self.frame_9)
        self.btn_HOME_2.setGeometry(QtCore.QRect(170, 710, 160, 70))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btn_HOME_2.setFont(font)
        self.btn_HOME_2.setAutoFillBackground(False)
        self.btn_HOME_2.setStyleSheet("background-color: rgb(255, 255, 127);")
        self.btn_HOME_2.setCheckable(False)
        self.btn_HOME_2.setAutoDefault(False)
        self.btn_HOME_2.setObjectName("btn_HOME_2")
        self.btn_RESET_2 = QtWidgets.QPushButton(self.frame_9)
        self.btn_RESET_2.setGeometry(QtCore.QRect(350, 710, 160, 70))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btn_RESET_2.setFont(font)
        self.btn_RESET_2.setAutoFillBackground(False)
        self.btn_RESET_2.setStyleSheet("background-color: rgb(231, 255, 215);")
        self.btn_RESET_2.setCheckable(False)
        self.btn_RESET_2.setAutoDefault(False)
        self.btn_RESET_2.setObjectName("btn_RESET_2")
        self.btn_START_2 = QtWidgets.QPushButton(self.frame_9)
        self.btn_START_2.setGeometry(QtCore.QRect(530, 710, 160, 70))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btn_START_2.setFont(font)
        self.btn_START_2.setAutoFillBackground(False)
        self.btn_START_2.setStyleSheet("background-color: rgb(85, 255, 0);")
        self.btn_START_2.setCheckable(False)
        self.btn_START_2.setAutoDefault(False)
        self.btn_START_2.setObjectName("btn_START_2")
        self.btn_STOP_2 = QtWidgets.QPushButton(self.frame_9)
        self.btn_STOP_2.setGeometry(QtCore.QRect(710, 710, 160, 70))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btn_STOP_2.setFont(font)
        self.btn_STOP_2.setAutoFillBackground(False)
        self.btn_STOP_2.setStyleSheet("background-color: rgb(170, 0, 0);")
        self.btn_STOP_2.setCheckable(False)
        self.btn_STOP_2.setAutoDefault(False)
        self.btn_STOP_2.setObjectName("btn_STOP_2")
        self.btn_VACCUM = QtWidgets.QPushButton(self.frame_9)
        self.btn_VACCUM.setGeometry(QtCore.QRect(1020, 530, 160, 70))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btn_VACCUM.setFont(font)
        self.btn_VACCUM.setAutoFillBackground(False)
        self.btn_VACCUM.setStyleSheet("background-color: rgb(231, 255, 215);")
        self.btn_VACCUM.setCheckable(False)
        self.btn_VACCUM.setAutoDefault(False)
        self.btn_VACCUM.setObjectName("btn_VACCUM")
        self.btn_TECHING = QtWidgets.QPushButton(self.frame_9)
        self.btn_TECHING.setGeometry(QtCore.QRect(1020, 610, 160, 70))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btn_TECHING.setFont(font)
        self.btn_TECHING.setAutoFillBackground(False)
        self.btn_TECHING.setStyleSheet("background-color: rgb(231, 255, 215);")
        self.btn_TECHING.setCheckable(False)
        self.btn_TECHING.setAutoDefault(False)
        self.btn_TECHING.setObjectName("btn_TECHING")
        self.label_error = QtWidgets.QLabel(self.frame_9)
        self.label_error.setGeometry(QtCore.QRect(10, 10, 1210, 40))
        font = QtGui.QFont()
        font.setPointSize(12)
        self.label_error.setFont(font)
        self.label_error.setStyleSheet("background-color: rgb(24, 24, 36);\n"
"color: rgb(255, 0, 0);")
        self.label_error.setObjectName("label_error")
        self.btn_RUN = QtWidgets.QPushButton(self.frame_9)
        self.btn_RUN.setGeometry(QtCore.QRect(890, 710, 160, 70))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btn_RUN.setFont(font)
        self.btn_RUN.setAutoFillBackground(False)
        self.btn_RUN.setStyleSheet("background-color: rgb(200, 96, 11);")
        self.btn_RUN.setCheckable(False)
        self.btn_RUN.setAutoDefault(False)
        self.btn_RUN.setObjectName("btn_RUN")
        self.btn_man = QtWidgets.QPushButton(self.frame_9)
        self.btn_man.setGeometry(QtCore.QRect(1020, 450, 160, 70))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btn_man.setFont(font)
        self.btn_man.setAutoFillBackground(False)
        self.btn_man.setStyleSheet("background-color: rgb(157, 157, 157);")
        self.btn_man.setCheckable(False)
        self.btn_man.setAutoDefault(False)
        self.btn_man.setObjectName("btn_man")
        self.label_39 = QtWidgets.QLabel(self.frame_9)
        self.label_39.setGeometry(QtCore.QRect(250, 550, 300, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_39.setFont(font)
        self.label_39.setStyleSheet("color: rgb(0, 255, 255);")
        self.label_39.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_39.setObjectName("label_39")
        self.label_38 = QtWidgets.QLabel(self.frame_9)
        self.label_38.setGeometry(QtCore.QRect(250, 470, 300, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_38.setFont(font)
        self.label_38.setStyleSheet("color: rgb(0, 255, 255);")
        self.label_38.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_38.setObjectName("label_38")
        self.lineEdit_TD_X = QtWidgets.QLineEdit(self.frame_9)
        self.lineEdit_TD_X.setGeometry(QtCore.QRect(250, 430, 200, 40))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(14)
        self.lineEdit_TD_X.setFont(font)
        self.lineEdit_TD_X.setStyleSheet("color: rgb(0, 255, 255);")
        self.lineEdit_TD_X.setObjectName("lineEdit_TD_X")
        self.label_36 = QtWidgets.QLabel(self.frame_9)
        self.label_36.setGeometry(QtCore.QRect(40, 440, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_36.setFont(font)
        self.label_36.setStyleSheet("color: rgb(0, 255, 255);")
        self.label_36.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_36.setObjectName("label_36")
        self.lineEdit_TD_Y = QtWidgets.QLineEdit(self.frame_9)
        self.lineEdit_TD_Y.setGeometry(QtCore.QRect(250, 510, 200, 40))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(14)
        self.lineEdit_TD_Y.setFont(font)
        self.lineEdit_TD_Y.setStyleSheet("color: rgb(0, 255, 255);")
        self.lineEdit_TD_Y.setObjectName("lineEdit_TD_Y")
        self.label_37 = QtWidgets.QLabel(self.frame_9)
        self.label_37.setGeometry(QtCore.QRect(40, 520, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_37.setFont(font)
        self.label_37.setStyleSheet("color: rgb(0, 255, 255);")
        self.label_37.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_37.setObjectName("label_37")
        self.lineEdit_TD_Z = QtWidgets.QLineEdit(self.frame_9)
        self.lineEdit_TD_Z.setGeometry(QtCore.QRect(250, 600, 200, 40))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(14)
        self.lineEdit_TD_Z.setFont(font)
        self.lineEdit_TD_Z.setStyleSheet("color: rgb(0, 255, 255);")
        self.lineEdit_TD_Z.setObjectName("lineEdit_TD_Z")
        self.label_40 = QtWidgets.QLabel(self.frame_9)
        self.label_40.setGeometry(QtCore.QRect(250, 640, 300, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_40.setFont(font)
        self.label_40.setStyleSheet("color: rgb(0, 255, 255);")
        self.label_40.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_40.setObjectName("label_40")
        self.label_41 = QtWidgets.QLabel(self.frame_9)
        self.label_41.setGeometry(QtCore.QRect(40, 610, 200, 30))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.label_41.setFont(font)
        self.label_41.setStyleSheet("color: rgb(0, 255, 255);")
        self.label_41.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_41.setObjectName("label_41")
        self.horizontalLayout_4.addWidget(self.frame_9)
        self.stackedWidget.addWidget(self.page_1)
        self.page_2 = QtWidgets.QWidget()
        self.page_2.setObjectName("page_2")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.page_2)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.frame_6 = QtWidgets.QFrame(self.page_2)
        self.frame_6.setStyleSheet("background-color: rgb(24, 24, 36);")
        self.frame_6.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_6.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_6.setObjectName("frame_6")
        self.horizontalLayout_12.addWidget(self.frame_6)
        self.stackedWidget.addWidget(self.page_2)
        self.page_5 = QtWidgets.QWidget()
        self.page_5.setObjectName("page_5")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.page_5)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.frame_16 = QtWidgets.QFrame(self.page_5)
        self.frame_16.setStyleSheet("background-color: rgb(24, 24, 36);")
        self.frame_16.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_16.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_16.setObjectName("frame_16")
        self.horizontalLayout_11.addWidget(self.frame_16)
        self.stackedWidget.addWidget(self.page_5)
        self.verticalLayout_7.addWidget(self.stackedWidget)
        self.verticalLayout_2.addWidget(self.main_body_contents)
        self.footer = QtWidgets.QFrame(self.main_body)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(14)
        font.setBold(True)
        font.setWeight(75)
        self.footer.setFont(font)
        self.footer.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.footer.setFrameShadow(QtWidgets.QFrame.Raised)
        self.footer.setObjectName("footer")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.footer)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.frame_10 = QtWidgets.QFrame(self.footer)
        self.frame_10.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_10.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_10.setObjectName("frame_10")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.frame_10)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setSpacing(0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.lb_model = QtWidgets.QLabel(self.frame_10)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.lb_model.setFont(font)
        self.lb_model.setStyleSheet("color: rgb(170, 255, 255);\n"
"")
        self.lb_model.setObjectName("lb_model")
        self.verticalLayout_3.addWidget(self.lb_model)
        self.horizontalLayout_3.addWidget(self.frame_10)
        self.frame_11 = QtWidgets.QFrame(self.footer)
        self.frame_11.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_11.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_11.setObjectName("frame_11")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.frame_11)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setSpacing(0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.system_active = QtWidgets.QPushButton(self.frame_11)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(15)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.system_active.setFont(font)
        self.system_active.setStyleSheet("color: rgb(0, 255, 255);")
        self.system_active.setCheckable(True)
        self.system_active.setObjectName("system_active")
        self.verticalLayout_4.addWidget(self.system_active, 0, QtCore.Qt.AlignRight)
        self.horizontalLayout_3.addWidget(self.frame_11)
        self.size_grip = QtWidgets.QFrame(self.footer)
        self.size_grip.setMinimumSize(QtCore.QSize(10, 10))
        self.size_grip.setMaximumSize(QtCore.QSize(10, 10))
        self.size_grip.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.size_grip.setFrameShadow(QtWidgets.QFrame.Raised)
        self.size_grip.setObjectName("size_grip")
        self.horizontalLayout_3.addWidget(self.size_grip)
        self.verticalLayout_2.addWidget(self.footer, 0, QtCore.Qt.AlignBottom)
        self.horizontalLayout.addWidget(self.main_body)
        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        self.stackedWidget.setCurrentIndex(1)
        self.btn_menu.toggled['bool'].connect(self.slide_menu_container.setVisible) # type: ignore
        self.btn_exit.clicked.connect(MainWindow.close) # type: ignore
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "MainWindow"))
        self.logo_main.setText(_translate("MainWindow", "Delta Robot"))
        self.btn_Home.setText(_translate("MainWindow", " Home"))
        self.btn_Manual.setText(_translate("MainWindow", " Manual"))
        self.btn_Setting.setText(_translate("MainWindow", " Setting"))
        self.btn_User.setText(_translate("MainWindow", " User"))
        self.btn_exit.setText(_translate("MainWindow", "Exit"))
        self.search_lineEdit.setPlaceholderText(_translate("MainWindow", "Search.... "))
        item = self.tableWidget_out.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Center X"))
        item = self.tableWidget_out.horizontalHeaderItem(1)
        item.setText(_translate("MainWindow", "Center Y"))
        item = self.tableWidget_out.horizontalHeaderItem(2)
        item.setText(_translate("MainWindow", "Center Z"))
        item = self.tableWidget_out.horizontalHeaderItem(3)
        item.setText(_translate("MainWindow", "Status"))
        self.label_15.setText(_translate("MainWindow", "Autorun Detection"))
        self.Gbox_ConnectDeltaRobot.setTitle(_translate("MainWindow", "Connect Delta Robot"))
        self.btn_START.setText(_translate("MainWindow", "STRAT"))
        self.btn_STOP.setText(_translate("MainWindow", "STOP"))
        self.label.setText(_translate("MainWindow", "PLC"))
        self.btn_ConnectPLC.setText(_translate("MainWindow", "Connect  PLC"))
        self.label_2.setText(_translate("MainWindow", "CAMERA"))
        self.btn_ConnectCamera.setText(_translate("MainWindow", "Connect Camera"))
        self.label_5.setText(_translate("MainWindow", "Name"))
        self.label_6.setText(_translate("MainWindow", "IP"))
        self.label_7.setText(_translate("MainWindow", "Text"))
        self.label_3.setText(_translate("MainWindow", "Name"))
        self.label_10.setText(_translate("MainWindow", "IP"))
        self.btn_Scan.setText(_translate("MainWindow", "Scan"))
        self.bnClose.setText(_translate("MainWindow", "Disconnect"))
        self.radioContinueMode.setText(_translate("MainWindow", "Auto"))
        self.radioTriggerMode.setText(_translate("MainWindow", "Trigger"))
        self.bnStop.setText(_translate("MainWindow", "Stop"))
        self.bnOpen.setText(_translate("MainWindow", "Open"))
        self.bnSaveImage.setText(_translate("MainWindow", "Save"))
        self.bnSoftwareTrigger.setText(_translate("MainWindow", "Set Trigger"))
        self.label_4.setText(_translate("MainWindow", "Exposure"))
        self.edtExposureTime.setText(_translate("MainWindow", "0"))
        self.label_13.setText(_translate("MainWindow", "Gain"))
        self.edtGain.setText(_translate("MainWindow", "0"))
        self.label_9.setText(_translate("MainWindow", "Frame rate"))
        self.edtFrameRate.setText(_translate("MainWindow", "0"))
        self.bnGetParam.setText(_translate("MainWindow", "Get Parameters"))
        self.bnSetParam.setText(_translate("MainWindow", "Setting Parameters"))
        self.label_14.setText(_translate("MainWindow", "Text"))
        self.bnStart.setText(_translate("MainWindow", "Start"))
        self.btn_RESET.setText(_translate("MainWindow", "RESET"))
        self.btn_HOME.setText(_translate("MainWindow", "HOME"))
        self.groupBox.setTitle(_translate("MainWindow", "Setting Delta"))
        self.label_22.setText(_translate("MainWindow", "Offset X"))
        self.label_23.setText(_translate("MainWindow", "Offset Y"))
        self.label_31.setText(_translate("MainWindow", "Sản Lượng Hiện Tại "))
        self.label_SL.setText(_translate("MainWindow", "0"))
        self.label_32.setText(_translate("MainWindow", "Tốc độ Robot "))
        self.lTocdoRB.setText(_translate("MainWindow", "0.0"))
        self.label_33.setText(_translate("MainWindow", "Tốc Độ Bẳng Tải "))
        self.lTocdoBT.setText(_translate("MainWindow", "0.0"))
        self.label_loaiSP.setText(_translate("MainWindow", "0"))
        self.label_34.setText(_translate("MainWindow", "Loại Sản Phẩm"))
        self.label_29.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Theta 1</p></body></html>"))
        self.label_28.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Theta 2</p></body></html>"))
        self.label_27.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Theta 3</p></body></html>"))
        self.label_24.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Axis X</p></body></html>"))
        self.label_25.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Axis Y</p></body></html>"))
        self.label_26.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Axis Z</p></body></html>"))
        self.tbThetaX.setText(_translate("MainWindow", "0"))
        self.tbThetaY.setText(_translate("MainWindow", "0"))
        self.tbThetaZ.setText(_translate("MainWindow", "0"))
        self.tbAxisX.setText(_translate("MainWindow", "0"))
        self.tbAxisY.setText(_translate("MainWindow", "0"))
        self.tbAxisZ.setText(_translate("MainWindow", "0"))
        self.label_30.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Pos_Y</p></body></html>"))
        self.label_35.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Pos_X</p></body></html>"))
        self.Pos_X.setText(_translate("MainWindow", "0"))
        self.Pos_Y.setText(_translate("MainWindow", "0"))
        self.label_42.setText(_translate("MainWindow", "QR Loại 2"))
        self.label_43.setText(_translate("MainWindow", "QR Loại 3"))
        self.label_qr2.setText(_translate("MainWindow", "0"))
        self.label_qr3.setText(_translate("MainWindow", "0"))
        self.btn_HOME_2.setText(_translate("MainWindow", "HOME"))
        self.btn_RESET_2.setText(_translate("MainWindow", "RESET"))
        self.btn_START_2.setText(_translate("MainWindow", "START"))
        self.btn_STOP_2.setText(_translate("MainWindow", "STOP"))
        self.btn_VACCUM.setText(_translate("MainWindow", "Vaccum"))
        self.btn_TECHING.setText(_translate("MainWindow", "Teching"))
        self.label_error.setText(_translate("MainWindow", "ERROR"))
        self.btn_RUN.setText(_translate("MainWindow", "RUN"))
        self.btn_man.setText(_translate("MainWindow", "MAN"))
        self.label_39.setText(_translate("MainWindow", "<html><head/><body><p>nhập từ (-120 ==&gt; 150)</p></body></html>"))
        self.label_38.setText(_translate("MainWindow", "<html><head/><body><p>nhập từ (-120 ==&gt; 120)</p></body></html>"))
        self.label_36.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Nhập Tọa Độ X</p></body></html>"))
        self.label_37.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Nhập Tọa Độ Y</p></body></html>"))
        self.label_40.setText(_translate("MainWindow", "<html><head/><body><p>nhập từ (-350 ==&gt; -450)</p></body></html>"))
        self.label_41.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Nhập Tọa Độ Z</p></body></html>"))
        self.lb_model.setText(_translate("MainWindow", "Nguyễn Vỉ Khang"))
        self.system_active.setText(_translate("MainWindow", "System"))
import icon_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    MainWindow = QtWidgets.QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(MainWindow)
    MainWindow.show()
    sys.exit(app.exec_())
