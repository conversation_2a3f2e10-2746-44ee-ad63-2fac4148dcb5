import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

e = 40
f = 100
re = 350
rf = 150

sqrt3 = np.sqrt(3.0)
pi = np.pi
sin120 = sqrt3 / 2.0
cos120 = -0.5
tan60 = sqrt3
sin30 = 0.5
tan30 = 1 / sqrt3

# ======= Kinematics Thuận (Forward Kinematics) =======
def delta_calcForward(theta1, theta2, theta3):
    t = (f - e) * tan30 / 2
    theta1, theta2, theta3 = np.radians([theta1, theta2, theta3])

    y1 = -(t + rf * np.cos(theta1))
    z1 = -rf * np.sin(theta1)

    y2 = (t + rf * np.cos(theta2)) * sin30
    x2 = y2 * tan60
    z2 = -rf * np.sin(theta2)

    y3 = (t + rf * np.cos(theta3)) * sin30
    x3 = -y3 * tan60
    z3 = -rf * np.sin(theta3)

    dnm = (y2 - y1) * x3 - (y3 - y1) * x2

    w1 = y1**2 + z1**2
    w2 = x2**2 + y2**2 + z2**2
    w3 = x3**2 + y3**2 + z3**2

    a1 = (z2 - z1) * (y3 - y1) - (z3 - z1) * (y2 - y1)
    b1 = -((w2 - w1) * (y3 - y1) - (w3 - w1) * (y2 - y1)) / 2.0
    a2 = -(z2 - z1) * x3 + (z3 - z1) * x2
    b2 = ((w2 - w1) * x3 - (w3 - w1) * x2) / 2.0

    a = a1**2 + a2**2 + dnm**2
    b = 2 * (a1 * b1 + a2 * (b2 - y1 * dnm) - z1 * dnm**2)
    c = (b2 - y1 * dnm)**2 + b1**2 + dnm**2 * (z1**2 - re**2)

    d = b**2 - 4.0 * a * c
    if d < 0:
        return None

    z0 = -0.5 * (b + np.sqrt(d)) / a
    x0 = (a1 * z0 + b1) / dnm
    y0 = (a2 * z0 + b2) / dnm

    return x0, y0, z0

# ======= Mô phỏng Robot Delta =======
def plot_delta_robot(theta1, theta2, theta3):
    result = delta_calcForward(theta1, theta2, theta3)
    if result is None:
        print("Không tìm thấy vị trí hợp lệ!")
        return

    x0, y0, z0 = result

    print(f"Tọa độ đầu cuối cho góc θ1={theta1}°, θ2={theta2}°, θ3={theta3}°:")
    print(f"  x = {x0:.2f} mm")
    print(f"  y = {y0:.2f} mm")
    print(f"  z = {z0:.2f} mm\n")

    fig = plt.figure(figsize=(10, 10))
    fig2 = plt.figure(figsize=(8, 8))
    ax1 = fig.add_subplot(111,projection='3d')
    ax2 = fig2.add_subplot(111)

    # 3D View (Mô phỏng 3D)
    # Define base and end-effector triangle points
    base_points = np.array([[50, -50, 0], [-50, -50, 0], [0, 100, 0], [50, -50, 0]])
    end_effector_points = np.array([[x0 + 20, y0 - 20, z0], [x0 - 20, y0 - 20, z0], [x0, y0 + 40, z0], [x0 + 20, y0 - 20, z0]])
    
    # Define arm linkages
    upper_arms = np.array([[[0, 0, 0], base_points[0]],
                            [[0, 0, 0], base_points[1]],
                            [[0, 0, 0], base_points[2]]])
    lower_arms = np.array([[base_points[0], end_effector_points[0]],
                            [base_points[1], end_effector_points[1]],
                            [base_points[2], end_effector_points[2]]])
    
    # Plot base and end effector
    ax1.plot(*base_points.T, 'ko-', label='Base')
    ax1.plot(*end_effector_points.T, 'ro-', label='End Effector')

    for arm in upper_arms:
        ax1.plot(*np.array(arm).T, 'g-', linewidth=2)
    for arm in lower_arms:
        ax1.plot(*np.array(arm).T, 'b-', linewidth=2)

    ax1.legend()
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    ax1.set_xlim([-150, 150])
    ax1.set_ylim([-150, 150])
    ax1.set_zlim([-600, 100])
    ax1.set_title('Delta Robot 3D View')
    ax1.text(x0, y0, z0, f"({x0:.2f}, {y0:.2f}, {z0:.2f})", fontsize=10, color='red')
    ax1.set_title(f"Delta Robot Kinematics\nθ1={theta1}°, θ2={theta2}°, θ3={theta3}°\nEnd Effector: ({x0:.2f}, {y0:.2f}, {z0:.2f})")

    ax2.cla()
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.plot(*base_points.T, 'ko-', label='Base')
    ax2.plot(*end_effector_points.T, 'ro-', label='End Effector')
    ax2.set_xlim([-150, 150])
    ax2.set_ylim([-150, 150])
    ax2.set_title('Delta Robot XY View')

    plt.tight_layout()
    plt.show()

theta1, theta2, theta3 = 65, 35, 50  # Góc động cơ
plot_delta_robot(theta1, theta2, theta3)
