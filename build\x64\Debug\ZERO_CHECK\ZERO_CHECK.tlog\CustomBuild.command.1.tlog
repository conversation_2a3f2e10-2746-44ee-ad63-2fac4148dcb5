^C:\DO_AN_TN\MAINFORM\BUILD\CMAKEFILES\FB9DDB38A63D34EA9EE1C8E664A1252E\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference -BC:/DO_AN_TN/MainForm/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/DO_AN_TN/MainForm/build/Yolov8CPPInference.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
