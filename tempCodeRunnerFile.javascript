/*_____________________________________________________________________________
__________  DO YOU HAVE WHAT IT TAKES TO FIND QUOKKA PRO TREASURE?  ___________
          |                   |                  |                     |
 _________|________________.=""_;=.______________|_____________________|_______
|                   |  ,-"_,=""     `"=.|                  |
|___________________|__"=._o`"-._        `"=.______________|___________________
          |                `"=._o`"=._      _`"=._                     |
 _________|_____________________:=._o "=._."_.-="'"=.__________________|_______
|                   |    __.--" , ; `"=._o." ,-"""-._ ".   |
|___________________|_._"  ,. .` ` `` ,  `"-._"-._   ". '__|___________________
          |           |o`"=._` , "` `; .". ,  "-._"-._; ;              |
 _________|___________| ;`-.o`"=._; ." ` '`."\` . "-._ /_______________|_______
|                   | |o;    `"-.o`"=._``  '` " ,__.--o;   |
|___________________|_| ;     (#) `-.o `"=.`_.--"_o.-; ;___|___________________
____/______/______/___|o;._    "      `".o|o_.--"    ;o;____/______/______/____
/______/______/______/_"=._o--._        ; | ;        ; ;/______/______/______/_
____/______/______/______/__"=._o--._   ;o|o;     _._;o;____/______/______/____
/______/______/______/______/____"=._o._; | ;_.--"o.--"_/______/______/______/_
____/______/______/______/______/_____"=.o|o_.--""___/______/______/______/____
/______/______/______/______/______/______/______/______/______/______/______*/

// Welcome to Quokka Pro Treasure Hunt! It only takes a minute to complete,
// and a very special surprise awaits those who can do it.

// If you get stuck, here's some pointers:
// Quick Module Install:  https://quokkajs.com/docs/#modules
// Live Comments:         https://quokkajs.com/docs/#comments
// Value Explorer:        https://quokkajs.com/docs/#value-explorer

// Good luck and enjoy!

// 1) Fix the error below and install the missing module for the current quokka
//    file using the link in the Quokka output or in the line hover message
//    or quick action.

const chest = require('quokka-treasure-chest')

// 2) So, you have found an old chest, there may be some treasure inside it.
//    You need to pass a correct string key to the "open" function.
//    The "open" function returns a clue about how to find the key.
//    Add Quokka live comment at the end of the line to see the returned value:
//    chest.open('paste_key_here') //?

chest.open('paste_key_here')
