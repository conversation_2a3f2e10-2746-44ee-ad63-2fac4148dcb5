from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QMessageBox, QPushButton, QHBoxLayout, QVBoxLayout, QLabel, QStackedWidget
from PyQt5.QtGui import  QPixmap, QImage
from PyQt5.QtCore import Qt, pyqtSlot, QTimer, QDateTime, QTimer, QEvent
import pypylon.pylon as pylon
from pyzbar import pyzbar
import sys
import cv2
import os

import icon_rc
from ui_form import Ui_MainWindow

class MainWindow(QMainWindow):
    def __init__(self):
        super(MainWindow, self).__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        self.ui.slide_menu_container.hide()
        self.ui.stackedWidget.setCurrentIndex(0)
        self.ui.btn_menu.setChecked(True)

        self.ConnectCamera = False
        self.camera = None
        self.factory = pylon.TlFactory.GetInstance()
        self.available_cameras = pylon.TlFactory.GetInstance().EnumerateDevices()

        self.ui.btn_Home.clicked.connect(self.on_btn_Home_clicked)
        self.ui.btn_Manual.clicked.connect(self.on_btn_Manual_clicked)
        self.ui.btn_Setting.clicked.connect(self.on_btn_Setting_clicked)
        self.ui.btn_User.clicked.connect(self.on_btn_User_clicked)
        self.ui.btn_exit.clicked.connect(self.on_btn_exit_clicked)
        self.ui.btn_menu.clicked.connect(self.on_btn_menu_clicked)

        self.ui.btn_ConnectCamera.clicked.connect(self.start_camera)
        # self.ui.btn_ConnectPLC.clicked.connect(self.open_camera)
        self.ui.btn_Start_Program.clicked.connect(self.set_CheckConnect)
        self.ui.btn_Stop_Program.clicked.connect(self.stop_camera)

## Functions for buttons menu
    @pyqtSlot(bool)
    def on_btn_menu_clicked(self, checked):
        if checked:
            self.ui.slide_menu_container.show()
        else:
            self.ui.slide_menu_container.hide()


## Functions for buttons Home
    @pyqtSlot(bool)
    def on_btn_Home_clicked(self, checked):
        self.ui.stackedWidget.setCurrentIndex(0)

## Functions for buttons Manual
    @pyqtSlot(bool)
    def on_btn_Manual_clicked(self, checked):
        self.ui.stackedWidget.setCurrentIndex(1)

## Functions for buttons Setting
    @pyqtSlot(bool)
    def on_btn_Setting_clicked(self, checked):
        self.ui.stackedWidget.setCurrentIndex(2)

## Functions for buttons User
    @pyqtSlot(bool)
    def on_btn_User_clicked(self, checked):
        self.ui.stackedWidget.setCurrentIndex(3)

## Functions for buttons Exit
    @pyqtSlot(bool)
    def on_btn_exit_clicked(self, checked):
        print("Exit Program")
        self.close()
        sys.exit()

## Functions for buttons Connect Camera
    @pyqtSlot(bool)
    def set_CheckConnect(self):
        if self.ConnectCamera:
            print("Is connect Camera")
        else:
            print("Is not connect Camera")
            QMessageBox.critical(self, "Error","Is not connect Camera")


    def start_camera(self):
        # Search connect camera 
        if len(self.available_cameras) == 0:
            print("No available cameras found")
        else:
            print(f"{len(self.available_cameras)} camera found:")
            for self.cam in self.available_cameras:
                print(self.cam.GetFriendlyName())
                self.ui.lb_namecamera.setText(self.cam.GetFriendlyName())
                print(self.cam.GetModelName())
                print(self.cam.GetIpAddress())
                self.ui.lb_ipcamera.setText(self.cam.GetIpAddress())

        # if have camera, start connect camera
        if len(self.available_cameras) > 0 and (self.camera is None or not self.camera.IsGrabbing()):
            self.ConnectCamera = True
            self.camera = pylon.InstantCamera(self.factory.CreateFirstDevice())
            self.camera.Open()
            print("Camera connected!")

            # Configuration camera to start a continuous capture
            self.camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

            # Create an object to convert images from Py pylon to OpenCV
            self.converter = pylon.ImageFormatConverter()
            self.converter.OutputPixelFormat = pylon.PixelType_BGR8packed
            self.converter.OutputBitAlignment = pylon.OutputBitAlignment_MsbAligned

            # Start QTimer to update the image
            self.timer = QTimer()
            self.timer.timeout.connect(self.update_frame)
            self.timer.start(10)  # update image every 10 ms
            
    def update_frame(self):
        # Lấy từng khung hình (frame) từ camera
        if self.camera.IsGrabbing():
            grab_result = self.camera.RetrieveResult(20000, pylon.TimeoutHandling_ThrowException)

            if grab_result.GrabSucceeded():
                # Chuyển đổi khung hình từ định dạng của pypylon sang định dạng của OpenCV
                image = self.converter.Convert(grab_result)
                frame = image.GetArray()
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frame = cv2.resize(frame, (640, 480))
                # Nhận diện và phân loại mã QR/mã vạch trong khung hình
                frame = self.detect_and_classify_barcode_qr(frame)
                
                # Chuyển khung hình thành định dạng QImage để hiển thị
                height, width, channel = frame.shape
                bytes_per_line = 3 * width
                q_img = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)
                
                # img = QImage(frame, frame.shape[1], frame.shape[0], frame.strides[0], QImage.Format_RGB888)
                self.ui.label_stream_camera.setPixmap(QPixmap.fromImage(q_img))

            grab_result.Release()  # Giải phóng khung hình sau khi xử lý xong

    def detect_and_classify_barcode_qr(self, frame):
        # Đọc và giải mã mã QR / Barcode
        decoded_objects = pyzbar.decode(frame)
        for obj in decoded_objects:
            # Lấy tọa độ khung của mã QR/Barcode
            (x, y, w, h) = obj.rect
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # Phân loại mã: QR hoặc Barcode
            qr_or_barcode = "QR Code" if obj.type == "QrCode" else "BarCode"

            # Lấy nội dung mã (chỉ in mã, không phải URL)
            content = obj.data.decode('utf-8').strip()
            
            # Thông tin nhận diện
            text = f"{qr_or_barcode}: {content}"
            cv2.putText(frame, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        return frame

## Functions for buttons Stop Camera
    @pyqtSlot(bool)
    def stop_camera(self):
        # Dừng việc chụp và đóng kết nối camera nếu đang chụp
        if self.camera and self.camera.IsGrabbing():
            self.camera.StopGrabbing()
            self.timer.stop()  # Dừng QTimer để ngưng cập nhật khung hình
        if self.camera:
            self.camera.Close()
        cv2.destroyAllWindows()
        self.ConnectCamera = False
        print("Stop Camera")

    def closeEvent(self, event):
        self.stop_camera()
        event.accept()
        super().closeEvent(event)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())