<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>2022</width>
    <height>1032</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1850</width>
    <height>1000</height>
   </size>
  </property>
  <property name="sizeIncrement">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="mouseTracking">
   <bool>false</bool>
  </property>
  <property name="windowTitle">
   <string>DetalRobot Software bt Factory Automation</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QWidget" name="verticalLayoutWidget_2">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>90</y>
      <width>1881</width>
      <height>761</height>
     </rect>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <widget class="QGroupBox" name="groupBox_6">
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="title">
            <string>Image Process</string>
           </property>
           <widget class="QLabel" name="lbImage">
            <property name="geometry">
             <rect>
              <x>240</x>
              <y>10</y>
              <width>471</width>
              <height>551</height>
             </rect>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(255, 255, 255);</string>
            </property>
            <property name="frameShape">
             <enum>QFrame::WinPanel</enum>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap>../../../Users/<USER>/Downloads/kisspng-delta-robot-parallel-manipulator-robotics-photo-printer-5adf2d537ceb15.8405795115245755715117.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
           <widget class="QWidget" name="verticalLayoutWidget_3">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>560</y>
              <width>931</width>
              <height>201</height>
             </rect>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <item>
              <widget class="QGroupBox" name="groupBox_3">
               <property name="font">
                <font>
                 <pointsize>12</pointsize>
                </font>
               </property>
               <property name="title">
                <string>Log File </string>
               </property>
               <widget class="QSlider" name="SLDetect">
                <property name="geometry">
                 <rect>
                  <x>230</x>
                  <y>40</y>
                  <width>551</width>
                  <height>22</height>
                 </rect>
                </property>
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
               </widget>
               <widget class="QSlider" name="Slider_2">
                <property name="geometry">
                 <rect>
                  <x>230</x>
                  <y>80</y>
                  <width>551</width>
                  <height>22</height>
                 </rect>
                </property>
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
               </widget>
               <widget class="QLabel" name="label">
                <property name="geometry">
                 <rect>
                  <x>20</x>
                  <y>40</y>
                  <width>161</width>
                  <height>31</height>
                 </rect>
                </property>
                <property name="text">
                 <string>Khả năng phát hiện :</string>
                </property>
               </widget>
               <widget class="QLabel" name="label_2">
                <property name="geometry">
                 <rect>
                  <x>20</x>
                  <y>80</y>
                  <width>81</width>
                  <height>31</height>
                 </rect>
                </property>
                <property name="text">
                 <string>Độ Sáng :</string>
                </property>
               </widget>
               <widget class="QLabel" name="label_3">
                <property name="geometry">
                 <rect>
                  <x>20</x>
                  <y>130</y>
                  <width>131</width>
                  <height>31</height>
                 </rect>
                </property>
                <property name="text">
                 <string>Thông Số HSV :</string>
                </property>
               </widget>
               <widget class="QLabel" name="label_5">
                <property name="geometry">
                 <rect>
                  <x>220</x>
                  <y>100</y>
                  <width>31</width>
                  <height>61</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>UH</string>
                </property>
               </widget>
               <widget class="QLineEdit" name="lineEdit">
                <property name="geometry">
                 <rect>
                  <x>250</x>
                  <y>120</y>
                  <width>113</width>
                  <height>20</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
               <widget class="QLineEdit" name="lineEdit_2">
                <property name="geometry">
                 <rect>
                  <x>500</x>
                  <y>120</y>
                  <width>113</width>
                  <height>20</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
               <widget class="QLabel" name="label_6">
                <property name="geometry">
                 <rect>
                  <x>470</x>
                  <y>100</y>
                  <width>31</width>
                  <height>61</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>UV</string>
                </property>
               </widget>
               <widget class="QLabel" name="label_10">
                <property name="geometry">
                 <rect>
                  <x>760</x>
                  <y>100</y>
                  <width>31</width>
                  <height>61</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>US</string>
                </property>
               </widget>
               <widget class="QLineEdit" name="lineEdit_3">
                <property name="geometry">
                 <rect>
                  <x>790</x>
                  <y>120</y>
                  <width>113</width>
                  <height>20</height>
                 </rect>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
               <widget class="QLabel" name="label_16">
                <property name="geometry">
                 <rect>
                  <x>220</x>
                  <y>130</y>
                  <width>31</width>
                  <height>61</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>DH</string>
                </property>
               </widget>
               <widget class="QLineEdit" name="lineEdit_4">
                <property name="geometry">
                 <rect>
                  <x>500</x>
                  <y>150</y>
                  <width>113</width>
                  <height>20</height>
                 </rect>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
               <widget class="QLineEdit" name="lineEdit_5">
                <property name="geometry">
                 <rect>
                  <x>790</x>
                  <y>150</y>
                  <width>113</width>
                  <height>20</height>
                 </rect>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
               <widget class="QLabel" name="label_18">
                <property name="geometry">
                 <rect>
                  <x>470</x>
                  <y>130</y>
                  <width>31</width>
                  <height>61</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>DV</string>
                </property>
               </widget>
               <widget class="QLineEdit" name="lineEdit_6">
                <property name="geometry">
                 <rect>
                  <x>250</x>
                  <y>150</y>
                  <width>113</width>
                  <height>20</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
               <widget class="QLabel" name="label_19">
                <property name="geometry">
                 <rect>
                  <x>760</x>
                  <y>130</y>
                  <width>31</width>
                  <height>61</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>DS</string>
                </property>
               </widget>
               <widget class="QLabel" name="numPH">
                <property name="geometry">
                 <rect>
                  <x>810</x>
                  <y>20</y>
                  <width>71</width>
                  <height>61</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Value</string>
                </property>
               </widget>
               <widget class="QLabel" name="numDS">
                <property name="geometry">
                 <rect>
                  <x>810</x>
                  <y>60</y>
                  <width>61</width>
                  <height>61</height>
                 </rect>
                </property>
                <property name="font">
                 <font>
                  <pointsize>15</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Value</string>
                </property>
               </widget>
               <widget class="QLabel" name="label_33">
                <property name="geometry">
                 <rect>
                  <x>290</x>
                  <y>170</y>
                  <width>471</width>
                  <height>31</height>
                 </rect>
                </property>
                <property name="text">
                 <string>Note : Tất cả Setting được lưu vào file Setting.text cùng thư mục</string>
                </property>
               </widget>
              </widget>
             </item>
            </layout>
           </widget>
           <widget class="QLineEdit" name="tbX">
            <property name="geometry">
             <rect>
              <x>40</x>
              <y>500</y>
              <width>111</width>
              <height>61</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <family>Bahnschrift SemiBold</family>
              <pointsize>20</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(255, 170, 127);</string>
            </property>
            <property name="text">
             <string>0</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="readOnly">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QLabel" name="label_15">
            <property name="geometry">
             <rect>
              <x>20</x>
              <y>460</y>
              <width>151</width>
              <height>41</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>20</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="frameShape">
             <enum>QFrame::Box</enum>
            </property>
            <property name="text">
             <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Position X&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
            </property>
           </widget>
           <widget class="QLineEdit" name="tbY">
            <property name="geometry">
             <rect>
              <x>780</x>
              <y>500</y>
              <width>111</width>
              <height>61</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <family>Bahnschrift SemiBold</family>
              <pointsize>20</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(255, 170, 127);</string>
            </property>
            <property name="text">
             <string>0</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="readOnly">
             <bool>true</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="btXPlus">
            <property name="geometry">
             <rect>
              <x>730</x>
              <y>30</y>
              <width>191</width>
              <height>71</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(170, 170, 127);</string>
            </property>
            <property name="text">
             <string>LX</string>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="btYMinus">
            <property name="geometry">
             <rect>
              <x>730</x>
              <y>340</y>
              <width>191</width>
              <height>71</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(170, 170, 127);</string>
            </property>
            <property name="text">
             <string>DY</string>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="btXMinus">
            <property name="geometry">
             <rect>
              <x>730</x>
              <y>120</y>
              <width>191</width>
              <height>71</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(170, 170, 127);</string>
            </property>
            <property name="text">
             <string>RX </string>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="btYPlus">
            <property name="geometry">
             <rect>
              <x>730</x>
              <y>250</y>
              <width>191</width>
              <height>71</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(170, 170, 127);</string>
            </property>
            <property name="text">
             <string>UY</string>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="btWidthPlus">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>30</y>
              <width>191</width>
              <height>71</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(170, 170, 127);</string>
            </property>
            <property name="text">
             <string>WX +</string>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="btHeightPlus">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>270</y>
              <width>191</width>
              <height>71</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(170, 170, 127);</string>
            </property>
            <property name="text">
             <string>HY +</string>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="btHeightMinus">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>360</y>
              <width>191</width>
              <height>71</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(170, 170, 127);</string>
            </property>
            <property name="text">
             <string>HY -</string>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
           <widget class="QPushButton" name="btWidthMinus">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>120</y>
              <width>191</width>
              <height>71</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>17</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(170, 170, 127);</string>
            </property>
            <property name="text">
             <string>WX -</string>
            </property>
            <property name="checkable">
             <bool>false</bool>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
           </widget>
           <widget class="QLabel" name="label_17">
            <property name="geometry">
             <rect>
              <x>760</x>
              <y>460</y>
              <width>151</width>
              <height>41</height>
             </rect>
            </property>
            <property name="font">
             <font>
              <pointsize>20</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="frameShape">
             <enum>QFrame::Box</enum>
            </property>
            <property name="text">
             <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Position Y&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
            </property>
           </widget>
           <widget class="QLabel" name="label_34">
            <property name="geometry">
             <rect>
              <x>260</x>
              <y>530</y>
              <width>381</width>
              <height>31</height>
             </rect>
            </property>
            <property name="text">
             <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Note : Chọn Vùng ROI phù hợp phát hiện sản phẩm&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
            </property>
           </widget>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_2">
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="autoFillBackground">
          <bool>false</bool>
         </property>
         <property name="title">
          <string>Control Panel</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
         <widget class="QPushButton" name="btReset">
          <property name="geometry">
           <rect>
            <x>280</x>
            <y>120</y>
            <width>161</width>
            <height>71</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <pointsize>20</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="autoFillBackground">
           <bool>false</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(255, 255, 127);</string>
          </property>
          <property name="text">
           <string>RESET</string>
          </property>
          <property name="checkable">
           <bool>false</bool>
          </property>
          <property name="autoDefault">
           <bool>false</bool>
          </property>
         </widget>
         <widget class="QGroupBox" name="groupBox">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>450</y>
            <width>931</width>
            <height>311</height>
           </rect>
          </property>
          <property name="title">
           <string>Setting Delta</string>
          </property>
          <widget class="QDoubleSpinBox" name="doubleSpinBox">
           <property name="geometry">
            <rect>
             <x>750</x>
             <y>140</y>
             <width>171</width>
             <height>31</height>
            </rect>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
           <property name="decimals">
            <number>2</number>
           </property>
           <property name="minimum">
            <double>-99.000000000000000</double>
           </property>
           <property name="singleStep">
            <double>1.000000000000000</double>
           </property>
          </widget>
          <widget class="QLabel" name="label_20">
           <property name="geometry">
            <rect>
             <x>590</x>
             <y>140</y>
             <width>71</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>Offset X:</string>
           </property>
          </widget>
          <widget class="QDoubleSpinBox" name="doubleSpinBox_2">
           <property name="geometry">
            <rect>
             <x>750</x>
             <y>190</y>
             <width>171</width>
             <height>31</height>
            </rect>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
           <property name="minimum">
            <double>-99.000000000000000</double>
           </property>
          </widget>
          <widget class="QLabel" name="label_21">
           <property name="geometry">
            <rect>
             <x>590</x>
             <y>190</y>
             <width>71</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>Offset Y:</string>
           </property>
          </widget>
          <widget class="QWidget" name="layoutWidget">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>40</y>
             <width>261</width>
             <height>308</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout">
            <item row="1" column="2">
             <widget class="QLineEdit" name="tbThetaY">
              <property name="font">
               <font>
                <family>Bahnschrift SemiBold</family>
                <pointsize>20</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QLineEdit" name="tbThetaZ">
              <property name="font">
               <font>
                <family>Bahnschrift SemiBold</family>
                <pointsize>20</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_12">
              <property name="font">
               <font>
                <pointsize>15</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="text">
               <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Axis X&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
              </property>
             </widget>
            </item>
            <item row="3" column="2">
             <widget class="QLineEdit" name="tbAxisX">
              <property name="font">
               <font>
                <family>Bahnschrift SemiBold</family>
                <pointsize>20</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="label_13">
              <property name="font">
               <font>
                <pointsize>15</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="text">
               <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Axis Y&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
              </property>
             </widget>
            </item>
            <item row="4" column="2">
             <widget class="QLineEdit" name="tbAxisY">
              <property name="font">
               <font>
                <family>Bahnschrift SemiBold</family>
                <pointsize>20</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="5" column="0">
             <widget class="QLabel" name="label_14">
              <property name="font">
               <font>
                <pointsize>15</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="text">
               <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Axis Z&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
              </property>
             </widget>
            </item>
            <item row="5" column="2">
             <widget class="QLineEdit" name="tbAxisZ">
              <property name="font">
               <font>
                <family>Bahnschrift SemiBold</family>
                <pointsize>20</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="2" column="0" colspan="2">
             <widget class="QLabel" name="label_9">
              <property name="font">
               <font>
                <pointsize>15</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="text">
               <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Theta Z&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_7">
              <property name="font">
               <font>
                <pointsize>15</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="text">
               <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Theta Y&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_8">
              <property name="font">
               <font>
                <pointsize>15</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="text">
               <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Theta X&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QLineEdit" name="tbThetaX">
              <property name="font">
               <font>
                <family>Bahnschrift SemiBold</family>
                <pointsize>20</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QLabel" name="label_23">
           <property name="geometry">
            <rect>
             <x>590</x>
             <y>40</y>
             <width>141</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>Set Số Sản Lượng :</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_24">
           <property name="geometry">
            <rect>
             <x>590</x>
             <y>90</y>
             <width>161</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>Sản Lượng Hiện Tại :</string>
           </property>
          </widget>
          <widget class="QLineEdit" name="lineEdit_7">
           <property name="geometry">
            <rect>
             <x>750</x>
             <y>40</y>
             <width>171</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>0</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLineEdit" name="lineEdit_8">
           <property name="geometry">
            <rect>
             <x>750</x>
             <y>90</y>
             <width>171</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>0</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="label_25">
           <property name="geometry">
            <rect>
             <x>350</x>
             <y>40</y>
             <width>121</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>Tốc độ Robot :</string>
           </property>
          </widget>
          <widget class="QLineEdit" name="lTocdoRB">
           <property name="geometry">
            <rect>
             <x>320</x>
             <y>80</y>
             <width>171</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>0.0</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="label_26">
           <property name="geometry">
            <rect>
             <x>340</x>
             <y>130</y>
             <width>141</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>Tốc Độ Bẳng Tải :</string>
           </property>
          </widget>
          <widget class="QLineEdit" name="lTocdoBT">
           <property name="geometry">
            <rect>
             <x>320</x>
             <y>170</y>
             <width>171</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>0.0</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLineEdit" name="lSoluong">
           <property name="geometry">
            <rect>
             <x>320</x>
             <y>260</y>
             <width>171</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>0</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="label_27">
           <property name="geometry">
            <rect>
             <x>320</x>
             <y>220</y>
             <width>201</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>Sản Phẩm Trên Băng Tải</string>
           </property>
          </widget>
          <widget class="QLabel" name="label_30">
           <property name="geometry">
            <rect>
             <x>540</x>
             <y>270</y>
             <width>311</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Note : Offset Robot Khi Cân Chỉnh Camera&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
          </widget>
         </widget>
         <widget class="QPushButton" name="btStop">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>120</y>
            <width>161</width>
            <height>71</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <pointsize>20</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="autoFillBackground">
           <bool>false</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(170, 0, 0);</string>
          </property>
          <property name="text">
           <string>STOP</string>
          </property>
          <property name="checkable">
           <bool>false</bool>
          </property>
          <property name="autoDefault">
           <bool>false</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="btStart">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>30</y>
            <width>161</width>
            <height>71</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <pointsize>20</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="autoFillBackground">
           <bool>false</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(231, 255, 215);</string>
          </property>
          <property name="text">
           <string>START</string>
          </property>
          <property name="checkable">
           <bool>false</bool>
          </property>
          <property name="autoDefault">
           <bool>false</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="btHome">
          <property name="geometry">
           <rect>
            <x>280</x>
            <y>30</y>
            <width>161</width>
            <height>71</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <pointsize>20</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="autoFillBackground">
           <bool>false</bool>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: rgb(255, 255, 127);</string>
          </property>
          <property name="text">
           <string>HOME</string>
          </property>
          <property name="checkable">
           <bool>false</bool>
          </property>
          <property name="autoDefault">
           <bool>false</bool>
          </property>
         </widget>
         <widget class="QGroupBox" name="groupBox_4">
          <property name="geometry">
           <rect>
            <x>500</x>
            <y>10</y>
            <width>421</width>
            <height>451</height>
           </rect>
          </property>
          <property name="title">
           <string>Status Connect</string>
          </property>
          <widget class="QPushButton" name="btConnect">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>40</y>
             <width>201</width>
             <height>71</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>20</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(85, 255, 0);</string>
           </property>
           <property name="text">
            <string>Connect</string>
           </property>
          </widget>
          <widget class="QPushButton" name="btDisconnect">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>140</y>
             <width>201</width>
             <height>71</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>20</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(255, 85, 0);</string>
           </property>
           <property name="text">
            <string>Disconnect</string>
           </property>
          </widget>
          <widget class="QLabel" name="machineStatus">
           <property name="geometry">
            <rect>
             <x>80</x>
             <y>240</y>
             <width>161</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Disconnected&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
          </widget>
          <widget class="QLabel" name="plcStatus">
           <property name="geometry">
            <rect>
             <x>80</x>
             <y>280</y>
             <width>161</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt; _______________&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
          </widget>
          <widget class="QPushButton" name="lGreen">
           <property name="geometry">
            <rect>
             <x>300</x>
             <y>40</y>
             <width>71</width>
             <height>71</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>20</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(255, 255, 255);</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="checkable">
            <bool>false</bool>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QPushButton" name="lRed">
           <property name="geometry">
            <rect>
             <x>300</x>
             <y>140</y>
             <width>71</width>
             <height>71</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>20</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(255, 255, 255);</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="checkable">
            <bool>false</bool>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QLabel" name="label_31">
           <property name="geometry">
            <rect>
             <x>30</x>
             <y>400</y>
             <width>381</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Note : Vui lòng kiểm tra Robot trước khi vận hành&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
          </widget>
          <widget class="QLabel" name="plcStatus_2">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>280</y>
             <width>161</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;PLC :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
          </widget>
          <widget class="QLabel" name="machineStatus_2">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>240</y>
             <width>161</width>
             <height>31</height>
            </rect>
           </property>
           <property name="text">
            <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Status :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
          </widget>
         </widget>
         <widget class="QGroupBox" name="groupBox_5">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>250</y>
            <width>501</width>
            <height>201</height>
           </rect>
          </property>
          <property name="title">
           <string>Manual </string>
          </property>
          <widget class="QPushButton" name="btTech">
           <property name="geometry">
            <rect>
             <x>20</x>
             <y>120</y>
             <width>161</width>
             <height>71</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>20</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(231, 255, 215);</string>
           </property>
           <property name="text">
            <string>Teching</string>
           </property>
           <property name="checkable">
            <bool>false</bool>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QPushButton" name="btVaccum">
           <property name="geometry">
            <rect>
             <x>20</x>
             <y>40</y>
             <width>161</width>
             <height>71</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>20</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(231, 255, 215);</string>
           </property>
           <property name="text">
            <string>Vaccum</string>
           </property>
           <property name="checkable">
            <bool>false</bool>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QPushButton" name="btBangtai1">
           <property name="geometry">
            <rect>
             <x>310</x>
             <y>40</y>
             <width>161</width>
             <height>71</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>20</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(231, 255, 215);</string>
           </property>
           <property name="text">
            <string>Băng Tải 1</string>
           </property>
           <property name="checkable">
            <bool>false</bool>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
          <widget class="QPushButton" name="btBangtai2">
           <property name="geometry">
            <rect>
             <x>310</x>
             <y>120</y>
             <width>161</width>
             <height>71</height>
            </rect>
           </property>
           <property name="font">
            <font>
             <pointsize>20</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(231, 255, 215);</string>
           </property>
           <property name="text">
            <string>Băng Tải 2</string>
           </property>
           <property name="checkable">
            <bool>false</bool>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </widget>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </widget>
   <widget class="QWidget" name="verticalLayoutWidget_4">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>850</y>
      <width>2926</width>
      <height>145</height>
     </rect>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout_4">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <property name="leftMargin">
        <number>1</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>1050</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item alignment="Qt::AlignVCenter">
        <widget class="QLabel" name="lbStatus">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>40</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(255, 170, 0);</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="text">
          <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Vui Lòng Kết Nối Chương Trình Với Robot Delta &lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
         </property>
         <property name="textFormat">
          <enum>Qt::AutoText</enum>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="indent">
          <number>0</number>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::NoTextInteraction</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </widget>
   <widget class="QLabel" name="label_11">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>-20</y>
      <width>1881</width>
      <height>111</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>0</width>
      <height>0</height>
     </size>
    </property>
    <property name="font">
     <font>
      <pointsize>60</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 255);</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::NoFrame</enum>
    </property>
    <property name="text">
     <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; color:#00aa7f;&quot;&gt;DELTA ROBOT SOFTWARE&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="indent">
     <number>0</number>
    </property>
    <property name="textInteractionFlags">
     <set>Qt::NoTextInteraction</set>
    </property>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>2022</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
