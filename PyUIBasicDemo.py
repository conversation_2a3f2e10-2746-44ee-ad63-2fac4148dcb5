# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'PyUIBasicDemo.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(787, 632)
        self.label = QtWidgets.QLabel(Form)
        self.label.setGeometry(QtCore.QRect(20, 20, 71, 16))
        self.label.setObjectName("label")
        self.label_2 = QtWidgets.QLabel(Form)
        self.label_2.setGeometry(QtCore.QRect(30, 60, 41, 16))
        self.label_2.setObjectName("label_2")
        self.ComboInterface = QtWidgets.QComboBox(Form)
        self.ComboInterface.setGeometry(QtCore.QRect(90, 20, 461, 22))
        self.ComboInterface.setObjectName("ComboInterface")
        self.ComboDevice = QtWidgets.QComboBox(Form)
        self.ComboDevice.setGeometry(QtCore.QRect(90, 60, 461, 22))
        self.ComboDevice.setObjectName("ComboDevice")
        self.label_3 = QtWidgets.QLabel(Form)
        self.label_3.setGeometry(QtCore.QRect(20, 110, 531, 441))
        self.label_3.setFrameShape(QtWidgets.QFrame.Box)
        self.label_3.setText("")
        self.label_3.setObjectName("label_3")
        self.groupBox = QtWidgets.QGroupBox(Form)
        self.groupBox.setGeometry(QtCore.QRect(570, 30, 201, 101))
        self.groupBox.setObjectName("groupBox")
        self.BtnEnumInterface = QtWidgets.QPushButton(self.groupBox)
        self.BtnEnumInterface.setGeometry(QtCore.QRect(30, 20, 141, 23))
        self.BtnEnumInterface.setObjectName("BtnEnumInterface")
        self.BtnOpenInterface = QtWidgets.QPushButton(self.groupBox)
        self.BtnOpenInterface.setGeometry(QtCore.QRect(10, 60, 81, 23))
        self.BtnOpenInterface.setObjectName("BtnOpenInterface")
        self.BtnCloseInterface = QtWidgets.QPushButton(self.groupBox)
        self.BtnCloseInterface.setGeometry(QtCore.QRect(110, 60, 81, 23))
        self.BtnCloseInterface.setObjectName("BtnCloseInterface")
        self.groupBox_2 = QtWidgets.QGroupBox(Form)
        self.groupBox_2.setGeometry(QtCore.QRect(570, 160, 201, 101))
        self.groupBox_2.setObjectName("groupBox_2")
        self.BtnEnumDevice = QtWidgets.QPushButton(self.groupBox_2)
        self.BtnEnumDevice.setGeometry(QtCore.QRect(30, 20, 141, 23))
        self.BtnEnumDevice.setObjectName("BtnEnumDevice")
        self.BtnOpenDevice = QtWidgets.QPushButton(self.groupBox_2)
        self.BtnOpenDevice.setGeometry(QtCore.QRect(10, 60, 81, 23))
        self.BtnOpenDevice.setObjectName("BtnOpenDevice")
        self.BtnCloseDevice = QtWidgets.QPushButton(self.groupBox_2)
        self.BtnCloseDevice.setGeometry(QtCore.QRect(110, 60, 81, 23))
        self.BtnCloseDevice.setObjectName("BtnCloseDevice")
        self.groupBox_3 = QtWidgets.QGroupBox(Form)
        self.groupBox_3.setGeometry(QtCore.QRect(570, 280, 201, 131))
        self.groupBox_3.setObjectName("groupBox_3")
        self.RadioContinuousMode = QtWidgets.QRadioButton(self.groupBox_3)
        self.RadioContinuousMode.setGeometry(QtCore.QRect(10, 20, 89, 16))
        self.RadioContinuousMode.setObjectName("RadioContinuousMode")
        self.RadioTriggerMode = QtWidgets.QRadioButton(self.groupBox_3)
        self.RadioTriggerMode.setGeometry(QtCore.QRect(110, 20, 89, 16))
        self.RadioTriggerMode.setObjectName("RadioTriggerMode")
        self.BtnStart = QtWidgets.QPushButton(self.groupBox_3)
        self.BtnStart.setGeometry(QtCore.QRect(10, 50, 81, 23))
        self.BtnStart.setObjectName("BtnStart")
        self.BtnStop = QtWidgets.QPushButton(self.groupBox_3)
        self.BtnStop.setGeometry(QtCore.QRect(110, 50, 81, 23))
        self.BtnStop.setObjectName("BtnStop")
        self.CheckTriggerbySoftware = QtWidgets.QCheckBox(self.groupBox_3)
        self.CheckTriggerbySoftware.setGeometry(QtCore.QRect(10, 90, 71, 21))
        self.CheckTriggerbySoftware.setObjectName("CheckTriggerbySoftware")
        self.BtnTriggerOnce = QtWidgets.QPushButton(self.groupBox_3)
        self.BtnTriggerOnce.setGeometry(QtCore.QRect(110, 90, 81, 23))
        self.BtnTriggerOnce.setObjectName("BtnTriggerOnce")
        self.groupBox_4 = QtWidgets.QGroupBox(Form)
        self.groupBox_4.setGeometry(QtCore.QRect(570, 430, 201, 121))
        self.groupBox_4.setObjectName("groupBox_4")
        self.BtnSaveBMP = QtWidgets.QPushButton(self.groupBox_4)
        self.BtnSaveBMP.setGeometry(QtCore.QRect(10, 30, 81, 23))
        self.BtnSaveBMP.setObjectName("BtnSaveBMP")
        self.BtnSaveJPEG = QtWidgets.QPushButton(self.groupBox_4)
        self.BtnSaveJPEG.setGeometry(QtCore.QRect(110, 30, 81, 23))
        self.BtnSaveJPEG.setObjectName("BtnSaveJPEG")
        self.BtnSaveTIFF = QtWidgets.QPushButton(self.groupBox_4)
        self.BtnSaveTIFF.setGeometry(QtCore.QRect(10, 80, 81, 23))
        self.BtnSaveTIFF.setObjectName("BtnSaveTIFF")
        self.BtnSavePNG = QtWidgets.QPushButton(self.groupBox_4)
        self.BtnSavePNG.setGeometry(QtCore.QRect(110, 80, 81, 23))
        self.BtnSavePNG.setObjectName("BtnSavePNG")

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "BasicDemo"))
        self.label.setText(_translate("Form", "Interface"))
        self.label_2.setText(_translate("Form", "Device"))
        self.groupBox.setTitle(_translate("Form", "采集卡"))
        self.BtnEnumInterface.setText(_translate("Form", "枚举采集卡"))
        self.BtnOpenInterface.setText(_translate("Form", "打开采集卡"))
        self.BtnCloseInterface.setText(_translate("Form", "关闭采集卡"))
        self.groupBox_2.setTitle(_translate("Form", "设备"))
        self.BtnEnumDevice.setText(_translate("Form", "枚举设备"))
        self.BtnOpenDevice.setText(_translate("Form", "打开设备"))
        self.BtnCloseDevice.setText(_translate("Form", "关闭设备"))
        self.groupBox_3.setTitle(_translate("Form", "图像采集"))
        self.RadioContinuousMode.setText(_translate("Form", "连续模式"))
        self.RadioTriggerMode.setText(_translate("Form", "触发模式"))
        self.BtnStart.setText(_translate("Form", "开始采集"))
        self.BtnStop.setText(_translate("Form", "停止采集"))
        self.CheckTriggerbySoftware.setText(_translate("Form", "软触发"))
        self.BtnTriggerOnce.setText(_translate("Form", "软触发一次"))
        self.groupBox_4.setTitle(_translate("Form", "图像保存"))
        self.BtnSaveBMP.setText(_translate("Form", "保存BMP"))
        self.BtnSaveJPEG.setText(_translate("Form", "保存JPEG"))
        self.BtnSaveTIFF.setText(_translate("Form", "保存TIFF"))
        self.BtnSavePNG.setText(_translate("Form", "保存PNG"))
