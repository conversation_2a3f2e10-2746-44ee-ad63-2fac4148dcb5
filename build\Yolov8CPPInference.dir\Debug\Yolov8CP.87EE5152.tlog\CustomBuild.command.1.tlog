^C:\DO_AN_TN\MAINFORM\ULTRALYTICS\EXAMPLES\YOLOV8-CPP-INFERENCE\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference -BC:/DO_AN_TN/MainForm/build --check-stamp-file C:/DO_AN_TN/MainForm/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
