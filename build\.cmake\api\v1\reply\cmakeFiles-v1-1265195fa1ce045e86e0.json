{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDAToolkit.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "C:/opencv/build/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "C:/opencv/build/OpenCVConfig.cmake"}, {"isExternal": true, "path": "C:/opencv/build/x64/vc16/lib/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "C:/opencv/build/x64/vc16/lib/OpenCVModules.cmake"}, {"isExternal": true, "path": "C:/opencv/build/x64/vc16/lib/OpenCVModules-debug.cmake"}, {"isExternal": true, "path": "C:/opencv/build/x64/vc16/lib/OpenCVModules-release.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/DO_AN_TN/MainForm/build", "source": "C:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference"}, "version": {"major": 1, "minor": 1}}