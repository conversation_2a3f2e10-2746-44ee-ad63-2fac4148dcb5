{"entries": [{"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/Hostx86/x64/lib.exe"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "c:/DO_AN_TN/MainForm/build"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "31"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "4"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "C:/Program Files/CMake/bin/cmake.exe"}, {"name": "CMAKE_CONFIGURATION_TYPES", "properties": [{"name": "HELPSTRING", "value": "Semicolon separated list of supported configuration types, only supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything else will be ignored."}], "type": "STRING", "value": "Debug;Release;MinSizeRel;RelWithDebInfo"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "C:/Program Files/CMake/bin/cpack.exe"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "C:/Program Files/CMake/bin/ctest.exe"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "/MDd /Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/MD /O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "/MD /O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/MD /Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C++ applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS /W3"}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "/MDd /Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/MD /O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "/MD /O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/MD /Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_C_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "Unknown"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "BOOL", "value": "TRUE"}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "C:/DO_AN_TN/MainForm/build/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Visual Studio 17 2022"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community"}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": "x64"}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": "host=x86"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "C:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "C:/Program Files/Yolov8CPPInference"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/Hostx86/x64/link.exe"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MT", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Windows Kits/10/bin/10.0.22621.0/x86/mt.exe"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "Yolov8CPPInference"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0.1"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "HELPSTRING", "value": "noop for ranlib"}], "type": "INTERNAL", "value": ":"}, {"name": "CMAKE_RC_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "RC compiler"}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Windows Kits/10/bin/10.0.22621.0/x86/rc.exe"}, {"name": "CMAKE_RC_COMPILER_WORKS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_RC_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during all build types."}], "type": "STRING", "value": "-DWIN32"}, {"name": "CMAKE_RC_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during DEBUG builds."}], "type": "STRING", "value": "-D_DEBUG"}, {"name": "CMAKE_RC_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "C:/Program Files/CMake/share/cmake-3.31"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "CUDAToolkit_BIN_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "PATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin"}, {"name": "CUDAToolkit_CUPTI_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include/../extras/CUPTI/include"}, {"name": "CUDAToolkit_NVCC_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/nvcc.exe"}, {"name": "CUDAToolkit_SENTINEL_FILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "CUDA_64_BIT_DEVICE_CODE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Compile device code in 64 bit mode"}], "type": "BOOL", "value": "ON"}, {"name": "CUDA_ATTACH_VS_BUILD_RULE_TO_CUDA_FILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Attach the build rule to the CUDA source file.  Enable only when the CUDA source file is added to at most one target."}], "type": "BOOL", "value": "ON"}, {"name": "CUDA_BUILD_CUBIN", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Generate and parse .cubin files in Device mode."}], "type": "BOOL", "value": "OFF"}, {"name": "CUDA_BUILD_EMULATION", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Build in Emulation mode"}], "type": "BOOL", "value": "OFF"}, {"name": "CUDA_CUDART", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cudart.lib"}, {"name": "CUDA_CUDART_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"cudart\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cudart.lib"}, {"name": "CUDA_CUDA_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"cuda\" library (older versions only)."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cuda.lib"}, {"name": "CUDA_GENERATED_OUTPUT_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Directory to put all the output files.  If blank it will default to the CMAKE_CURRENT_BINARY_DIR"}], "type": "PATH", "value": ""}, {"name": "CUDA_HOST_COMPILATION_CPP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Generated file extension"}], "type": "BOOL", "value": "ON"}, {"name": "CUDA_HOST_COMPILER", "properties": [{"name": "HELPSTRING", "value": "Host side compiler used by NVCC"}], "type": "FILEPATH", "value": "$(VCInstallDir)Tools/MSVC/$(VCToolsVersion)/bin/Host$(Platform)/$(PlatformTarget)"}, {"name": "CUDA_NVCC_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/nvcc.exe"}, {"name": "CUDA_NVCC_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Semi-colon delimit multiple arguments. during all build types."}], "type": "STRING", "value": ""}, {"name": "CUDA_NVCC_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Semi-colon delimit multiple arguments. during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CUDA_NVCC_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Semi-colon delimit multiple arguments. during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CUDA_NVCC_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Semi-colon delimit multiple arguments. during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CUDA_NVCC_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Semi-colon delimit multiple arguments. during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CUDA_OpenCL_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"OpenCL\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/OpenCL.lib"}, {"name": "CUDA_PROPAGATE_HOST_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Propagate C/CXX_FLAGS and friends to the host compiler via -Xcompile"}], "type": "BOOL", "value": "ON"}, {"name": "CUDA_SDK_ROOT_DIR", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "CUDA_SDK_ROOT_DIR-NOTFOUND"}, {"name": "CUDA_SDK_ROOT_DIR_INTERNAL", "properties": [{"name": "HELPSTRING", "value": "This is the value of the last time CUDA_SDK_ROOT_DIR was set successfully."}], "type": "INTERNAL", "value": "CUDA_SDK_ROOT_DIR-NOTFOUND"}, {"name": "CUDA_SEPARABLE_COMPILATION", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Compile CUDA objects with separable compilation enabled.  Requires CUDA 5.0+"}], "type": "BOOL", "value": "OFF"}, {"name": "CUDA_TOOLKIT_INCLUDE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include"}, {"name": "CUDA_TOOLKIT_ROOT_DIR_INTERNAL", "properties": [{"name": "HELPSTRING", "value": "This is the value of the last time CUDA_TOOLKIT_ROOT_DIR was set successfully."}], "type": "INTERNAL", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.0"}, {"name": "CUDA_TOOLKIT_TARGET_DIR_INTERNAL", "properties": [{"name": "HELPSTRING", "value": "This is the value of the last time CUDA_TOOLKIT_TARGET_DIR was set successfully."}], "type": "INTERNAL", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.0"}, {"name": "CUDA_USE_STATIC_CUDA_RUNTIME", "properties": [{"name": "HELPSTRING", "value": "Use the static version of the CUDA runtime library if available"}], "type": "INTERNAL", "value": "OFF"}, {"name": "CUDA_VERBOSE_BUILD", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Print out the commands run while compiling the CUDA source file.  With the Makefile generator this defaults to VERBOSE variable specified on the command line, but can be forced on with this option."}], "type": "BOOL", "value": "OFF"}, {"name": "CUDA_VERSION", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Version of CUDA as computed from nvcc."}], "type": "STRING", "value": "12.8"}, {"name": "CUDA_cuFile_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cuFile_LIBRARY-NOTFOUND"}, {"name": "CUDA_cuFile_rdma_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cuFile_rdma_LIBRARY-NOTFOUND"}, {"name": "CUDA_cuFile_rdma_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cuFile_rdma_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_cuFile_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cuFile_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_cublasLt_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cublasLt.lib"}, {"name": "CUDA_cublasLt_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cublasLt_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_cublas_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"cublas\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cublas.lib"}, {"name": "CUDA_cublas_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cublas_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_cuda_driver_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cuda.lib"}, {"name": "CUDA_cudadevrt_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"cudadevrt\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cudadevrt.lib"}, {"name": "CUDA_cudart_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cudart.lib"}, {"name": "CUDA_cudart_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "static CUDA runtime library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cudart_static.lib"}, {"name": "CUDA_cudla_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cudla_LIBRARY-NOTFOUND"}, {"name": "CUDA_cufft_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"cufft\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cufft.lib"}, {"name": "CUDA_cufft_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cufft_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_cufft_static_nocallback_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cufft_static_nocallback_LIBRARY-NOTFOUND"}, {"name": "CUDA_cufftw_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cufftw.lib"}, {"name": "CUDA_cufftw_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cufftw_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_culibos_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_culibos_LIBRARY-NOTFOUND"}, {"name": "CUDA_cupti_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"cupti\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/extras/CUPTI/lib64/cupti.lib"}, {"name": "CUDA_cupti_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cupti_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_curand_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"curand\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/curand.lib"}, {"name": "CUDA_curand_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_curand_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_cusolver_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"cusolver\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cusolver.lib"}, {"name": "CUDA_cusolver_lapack_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cusolver_lapack_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_cusolver_metis_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cusolver_metis_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_cusolver_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cusolver_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_cusparse_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"cusparse\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cusparse.lib"}, {"name": "CUDA_cusparse_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_cusparse_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_make2cmake", "properties": [{"name": "HELPSTRING", "value": "Location of make2cmake.cmake"}], "type": "INTERNAL", "value": "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDA/make2cmake.cmake"}, {"name": "CUDA_nppc_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppc\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppc.lib"}, {"name": "CUDA_nppc_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppc_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppial_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppial\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppial.lib"}, {"name": "CUDA_nppial_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppial_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppicc_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppicc\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppicc.lib"}, {"name": "CUDA_nppicc_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppicc_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppicom_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppicom_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppicom_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppicom_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppidei_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppidei\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppidei.lib"}, {"name": "CUDA_nppidei_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppidei_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppif_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppif\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppif.lib"}, {"name": "CUDA_nppif_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppif_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppig_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppig\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppig.lib"}, {"name": "CUDA_nppig_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppig_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppim_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppim\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppim.lib"}, {"name": "CUDA_nppim_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppim_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppist_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppist\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppist.lib"}, {"name": "CUDA_nppist_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppist_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppisu_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppisu\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppisu.lib"}, {"name": "CUDA_nppisu_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppisu_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nppitc_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nppitc\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nppitc.lib"}, {"name": "CUDA_nppitc_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nppitc_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_npps_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"npps\" library"}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/npps.lib"}, {"name": "CUDA_npps_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_npps_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvJitLink_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvJitLink.lib"}, {"name": "CUDA_nvJitLink_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvJitLink_static.lib"}, {"name": "CUDA_nvToolsExt_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nvToolsExt\" library"}], "type": "FILEPATH", "value": "CUDA_nvToolsExt_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvcuvenc_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nvcuvenc\" library"}], "type": "FILEPATH", "value": "CUDA_nvcuvenc_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvcuvid_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "\"nvcuvid\" library"}], "type": "FILEPATH", "value": "CUDA_nvcuvid_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvfatbin_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvfatbin.lib"}, {"name": "CUDA_nvfatbin_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvfatbin_static.lib"}, {"name": "CUDA_nvgraph_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nvgraph_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvgraph_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nvgraph_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvjpeg_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvjpeg.lib"}, {"name": "CUDA_nvjpeg_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nvjpeg_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvml_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvml.lib"}, {"name": "CUDA_nvml_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nvml_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvperf_host_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/extras/CUPTI/lib64/nvperf_host.lib"}, {"name": "CUDA_nvperf_host_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nvperf_host_static_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvperf_target_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/extras/CUPTI/lib64/nvperf_target.lib"}, {"name": "CUDA_nvptxcompiler_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvptxcompiler_static.lib"}, {"name": "CUDA_nvrtc_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvrtc.lib"}, {"name": "CUDA_nvrtc_builtins_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "CUDA_nvrtc_builtins_LIBRARY-NOTFOUND"}, {"name": "CUDA_nvrtc_builtins_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvrtc-builtins_static.lib"}, {"name": "CUDA_nvrtc_static_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/nvrtc_static.lib"}, {"name": "CUDA_parse_cubin", "properties": [{"name": "HELPSTRING", "value": "Location of parse_cubin.cmake"}], "type": "INTERNAL", "value": "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDA/parse_cubin.cmake"}, {"name": "CUDA_pcsamplingutil_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/extras/CUPTI/lib64/pcsamplingutil.lib"}, {"name": "CUDA_run_nvcc", "properties": [{"name": "HELPSTRING", "value": "Location of run_nvcc.cmake"}], "type": "INTERNAL", "value": "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDA/run_nvcc.cmake"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_CUDA", "properties": [{"name": "HELPSTRING", "value": "Details about finding CUDA"}], "type": "INTERNAL", "value": "[C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.0][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/nvcc.exe][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cudart.lib][v12.8(11)]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_CUDAToolkit", "properties": [{"name": "HELPSTRING", "value": "Details about finding CUDAToolkit"}], "type": "INTERNAL", "value": "[C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64/cudart.lib][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin][v12.8.61()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenCV", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenCV"}], "type": "INTERNAL", "value": "[C:/opencv/build][v4.10.0()]"}, {"name": "OpenCV_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for OpenCV."}], "type": "PATH", "value": "C:/opencv/build"}, {"name": "Yolov8CPPInference_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "C:/DO_AN_TN/MainForm/build"}, {"name": "Yolov8CPPInference_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "Yolov8CPPInference_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "C:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference"}, {"name": "_cmake_CUDAToolkit_implicit_link_directories", "properties": [{"name": "HELPSTRING", "value": "CUDAToolkit internal list of implicit link directories"}], "type": "INTERNAL", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64"}, {"name": "_cmake_CUDAToolkit_include_directories", "properties": [{"name": "HELPSTRING", "value": "CUDAToolkit internal list of include directories"}], "type": "INTERNAL", "value": "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include"}], "kind": "cache", "version": {"major": 2, "minor": 0}}