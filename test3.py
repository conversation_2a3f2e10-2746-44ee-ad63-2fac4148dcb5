import sys
import numpy as np
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QLabel, QLineEdit, QPushButton, QVBoxLayout, QHBoxLayout, QGridLayout
from PyQt5.QtGui import QFont

class DeltaRobotApp(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        self.setWindowTitle('Delta Robot Kinematics')
        self.setGeometry(100, 100, 600, 400)
        
        layout = QVBoxLayout()
        grid_layout = QGridLayout()
        
        font = QFont("Time New Roman", 14)
        
        labels = ["Base Radius (F):", "Bicep Length (RF):", "Forearm Length (RE):", "End Effector Radius (E):", 
                  "Base To Floor Distance (B):", "Steps Per Turn:", "Motor 1:", "Motor 2:", "Motor 3:"]
        
        self.inputs = []
        default_values = ["100.0", "150.0", "350.0", "40.0", "600.0", "3200.0", "0.0", "0.0", "0.0"]
        
        for i, label in enumerate(labels):
            lbl = QLabel(label)
            lbl.setFont(font)
            input_field = QLineEdit()
            input_field.setText(default_values[i])
            grid_layout.addWidget(lbl, i, 0)
            grid_layout.addWidget(input_field, i, 1)
            self.inputs.append(input_field)
        
        self.btn_forward = QPushButton("Forward Kinematics")
        
        self.btn_forward.clicked.connect(self.forward_kinematics)
        
        layout.addLayout(grid_layout)
        layout.addWidget(self.btn_forward)
        
        self.result_label = QLabel("Result: X = 0, Y = 0, Z = 0")
        self.result_label.setFont(font)
        layout.addWidget(self.result_label)
        
        self.setLayout(layout)
    
    def forward_kinematics(self):
        theta1 = float(self.inputs[6].text())
        theta2 = float(self.inputs[7].text())
        theta3 = float(self.inputs[8].text())
        
        result = self.calculate_forward(theta1, theta2, theta3)
        print("Nhập tọa độ góc theta:", theta1, theta2, theta3)
        if result:
            self.result_label.setText(f"Result: X = {result[0]:.2f}, Y = {result[1]:.2f}, Z = {result[2]:.2f}")
            print(f"Tọa độ của khâu cuối: X = {result[0]:.2f}, Y = {result[1]:.2f}, Z = {result[2]:.2f}")
        else:
            self.result_label.setText("Invalid position")
            print("Không tìm thấy vị trí hợp lệ!")
    
    def calculate_forward(self, theta1, theta2, theta3):
        rf = float(self.inputs[1].text())
        re = float(self.inputs[2].text())
        f = float(self.inputs[0].text())
        e = float(self.inputs[3].text())
        
        sqrt3 = np.sqrt(3.0)
        tan30 = 1 / sqrt3
        t = (f - e) * tan30 / 2
        theta1, theta2, theta3 = np.radians([theta1, theta2, theta3])
        
        y1 = -(t + rf * np.cos(theta1))
        z1 = -rf * np.sin(theta1)
        
        y2 = (t + rf * np.cos(theta2)) * 0.5
        x2 = y2 * sqrt3
        z2 = -rf * np.sin(theta2)
        
        y3 = (t + rf * np.cos(theta3)) * 0.5
        x3 = -y3 * sqrt3
        z3 = -rf * np.sin(theta3)
        
        dnm = (y2 - y1) * x3 - (y3 - y1) * x2
        w1 = y1**2 + z1**2
        w2 = x2**2 + y2**2 + z2**2
        w3 = x3**2 + y3**2 + z3**2
        
        a1 = (z2 - z1) * (y3 - y1) - (z3 - z1) * (y2 - y1)
        b1 = -((w2 - w1) * (y3 - y1) - (w3 - w1) * (y2 - y1)) / 2.0
        a2 = -(z2 - z1) * x3 + (z3 - z1) * x2
        b2 = ((w2 - w1) * x3 - (w3 - w1) * x2) / 2.0
        
        a = a1**2 + a2**2 + dnm**2
        b = 2 * (a1 * b1 + a2 * (b2 - y1 * dnm) - z1 * dnm**2)
        c = (b2 - y1 * dnm)**2 + b1**2 + dnm**2 * (z1**2 - re**2)
        
        d = b**2 - 4.0 * a * c
        if d < 0:
            return None
        
        z0 = -0.5 * (b + np.sqrt(d)) / a
        x0 = (a1 * z0 + b1) / dnm
        y0 = (a2 * z0 + b2) / dnm
        
        return x0, y0, z0

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = DeltaRobotApp()
    ex.show()
    sys.exit(app.exec_())
