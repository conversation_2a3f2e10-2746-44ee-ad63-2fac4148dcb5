{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 17 2022", "platform": "x64"}, "paths": {"cmake": "C:/Program Files/CMake/bin/cmake.exe", "cpack": "C:/Program Files/CMake/bin/cpack.exe", "ctest": "C:/Program Files/CMake/bin/ctest.exe", "root": "C:/Program Files/CMake/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 4, "string": "3.31.4", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-91cf13648e8233a53a29.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-c269b820663829e3d0d7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1265195fa1ce045e86e0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-4230d8495687f53a8b8e.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-c269b820663829e3d0d7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-91cf13648e8233a53a29.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-4230d8495687f53a8b8e.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1265195fa1ce045e86e0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}