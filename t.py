# -- coding: utf-8 --
import msvcrt
import numpy as np
import datetime
import inspect
import random
import sys
import cv2
import icon_rc
import snap7
import time
import os
import threading
from threading import Lock
import logging
import pypylon.pylon as pylon
from PyQt5.QtGui import  *
from PyQt5.QtCore import *
from ultralytics import YOLO
from ctypes import *
from PyQt5.QtWidgets import *
from pyzbar.pyzbar import decode
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
from snap7.util import get_bool, get_real, set_bool, get_int, set_int
from UI_form import Ui_MainWindow

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
sys.path.append("C:/Users/<USER>/OneDrive/Dokumen/GitHub/Python/MvImport")

plc = snap7.client.Client()
ip = "***********"  
rack = 0           
slot = 1          
db_number = 1    
byte_index = 0

def TxtWrapBy(start_str, end_str, all_str):
    start = all_str.find(start_str)
    if start == -1:
        return None  
    start += len(start_str)
    end = all_str.find(end_str, start)
    if end == -1:
        return None
    return all_str[start:end].strip()

def ToHexStr(num):
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    if num == 0:
        return "0"
    if num < 0:
        num = num + 2 ** 32
    hexStr = ""
    while num > 0:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    return hexStr

def Async_raise(tid, exctype):
    tid = ctypes.c_long(tid)
    if not inspect.isclass(exctype):
        exctype = type(exctype)
    res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
    if res == 0:
        raise ValueError("invalid thread id")
    elif res != 1:
        ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
        raise SystemError("PyThreadState_SetAsyncExc failed")

def Stop_thread(thread):
    Async_raise(thread.ident, SystemExit)

def To_hex_str(num):
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    hexStr = ""
    if num < 0:
        num = num + 2 ** 32
    while num >= 16:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    hexStr = chaDic.get(num, str(num)) + hexStr
    return hexStr

def Is_mono_data(enGvspPixelType):
    if PixelType_Gvsp_Mono8 == enGvspPixelType or PixelType_Gvsp_Mono10 == enGvspPixelType \
            or PixelType_Gvsp_Mono10_Packed == enGvspPixelType or PixelType_Gvsp_Mono12 == enGvspPixelType \
            or PixelType_Gvsp_Mono12_Packed == enGvspPixelType:
        return True
    else:
        return False

def Is_color_data(enGvspPixelType):
    if PixelType_Gvsp_BayerGR8 == enGvspPixelType or PixelType_Gvsp_BayerRG8 == enGvspPixelType \
            or PixelType_Gvsp_BayerGB8 == enGvspPixelType or PixelType_Gvsp_BayerBG8 == enGvspPixelType \
            or PixelType_Gvsp_BayerGR10 == enGvspPixelType or PixelType_Gvsp_BayerRG10 == enGvspPixelType \
            or PixelType_Gvsp_BayerGB10 == enGvspPixelType or PixelType_Gvsp_BayerBG10 == enGvspPixelType \
            or PixelType_Gvsp_BayerGR12 == enGvspPixelType or PixelType_Gvsp_BayerRG12 == enGvspPixelType \
            or PixelType_Gvsp_BayerGB12 == enGvspPixelType or PixelType_Gvsp_BayerBG12 == enGvspPixelType \
            or PixelType_Gvsp_BayerGR10_Packed == enGvspPixelType or PixelType_Gvsp_BayerRG10_Packed == enGvspPixelType \
            or PixelType_Gvsp_BayerGB10_Packed == enGvspPixelType or PixelType_Gvsp_BayerBG10_Packed == enGvspPixelType \
            or PixelType_Gvsp_BayerGR12_Packed == enGvspPixelType or PixelType_Gvsp_BayerRG12_Packed == enGvspPixelType \
            or PixelType_Gvsp_BayerGB12_Packed == enGvspPixelType or PixelType_Gvsp_BayerBG12_Packed == enGvspPixelType \
            or PixelType_Gvsp_YUV422_Packed == enGvspPixelType or PixelType_Gvsp_YUV422_YUYV_Packed == enGvspPixelType:
        return True
    else:
        return False

def Mono_numpy(data, nWidth, nHeight):
    data_ = np.frombuffer(data, count=int(nWidth * nHeight), dtype=np.uint8, offset=0)
    data_mono_arr = data_.reshape(nHeight, nWidth)
    numArray = np.zeros([nHeight, nWidth, 1], "uint8")
    numArray[:, :, 0] = data_mono_arr
    return numArray

def Color_numpy(data, nWidth, nHeight):
    data_ = np.frombuffer(data, count=int(nWidth * nHeight * 3), dtype=np.uint8, offset=0)
    data_r = data_[0:nWidth * nHeight * 3:3]
    data_g = data_[1:nWidth * nHeight * 3:3]
    data_b = data_[2:nWidth * nHeight * 3:3]

    data_r_arr = data_r.reshape(nHeight, nWidth)
    data_g_arr = data_g.reshape(nHeight, nWidth)
    data_b_arr = data_b.reshape(nHeight, nWidth)
    numArray = np.zeros([nHeight, nWidth, 3], "uint8")

    numArray[:, :, 0] = data_r_arr
    numArray[:, :, 1] = data_g_arr
    numArray[:, :, 2] = data_b_arr
    return numArray

####################*****************#######################
class CameraOperation(QObject):
    my_signal = pyqtSignal(str)
    coordinates_signal = pyqtSignal(int, int)
    code_signal = pyqtSignal(str)
    def __init__(self, obj_cam, st_device_list, n_connect_num=0, b_open_device=False, b_start_grabbing=False,
                 h_thread_handle=None, b_thread_closed=False, st_frame_info=None, b_exit=False, b_save_bmp=False,
                 b_save_jpg=False, buf_save_image=None, n_save_image_size=0, n_win_gui_id=0, frame_rate=0,
                 exposure_time=0, gain=0):
        super().__init__()
        self.obj_cam = obj_cam
        self.st_device_list = st_device_list
        self.n_connect_num = n_connect_num
        self.b_open_device = b_open_device
        self.b_start_grabbing = b_start_grabbing
        self.b_thread_closed = b_thread_closed
        self.st_frame_info = st_frame_info
        self.b_exit = b_exit
        self.b_save_bmp = b_save_bmp
        self.b_save_jpg = b_save_jpg
        self.buf_save_image = buf_save_image
        self.n_save_image_size = n_save_image_size
        self.h_thread_handle = h_thread_handle
        self.b_thread_closed
        self.frame_rate = frame_rate
        self.exposure_time = exposure_time
        self.gain = gain
        self.plc_controller = PLC_Thread()
        self.barcode_data_list = []

        self.model = YOLO('C:\\DO_AN_TN\\MainForm\\DETECT_BOX\\runs\\detect\\train\\weights\\last.pt')
        self.buf_lock = threading.Lock()
        self.coordinates_signal.connect(self.plc_controller.display_first_box)
        self.code_signal.connect(self.plc_controller.display_second_code)
    
    def start(self):
        self.send_message("Connect!")
        self.send_coordinates()
    
    def send_message(self, message):
        print("Sender: Đang phát tín hiệu...")
        self.my_signal.emit(message)

    def Open_device(self):
        if not self.b_open_device:
            if self.n_connect_num < 0:
                return MV_E_CALLORDER

            nConnectionNum = int(self.n_connect_num)
            stDeviceList = cast(self.st_device_list.pDeviceInfo[int(nConnectionNum)],
                                POINTER(MV_CC_DEVICE_INFO)).contents
            self.obj_cam = MvCamera()
            ret = self.obj_cam.MV_CC_CreateHandle(stDeviceList)
            if ret != 0:
                self.obj_cam.MV_CC_DestroyHandle()
                return ret

            ret = self.obj_cam.MV_CC_OpenDevice()
            if ret != 0:
                return ret
            print("open device successfully!")
            self.b_open_device = True
            self.b_thread_closed = False

            if stDeviceList.nTLayerType == MV_GIGE_DEVICE:
                nPacketSize = self.obj_cam.MV_CC_GetOptimalPacketSize()
                if int(nPacketSize) > 0:
                    ret = self.obj_cam.MV_CC_SetIntValue("GevSCPSPacketSize", nPacketSize)
                    if ret != 0:
                        print("warning: set packet size fail! ret[0x%x]" % ret)
                else:
                    print("warning: set packet size fail! ret[0x%x]" % nPacketSize)

            stBool = c_bool(False)
            ret = self.obj_cam.MV_CC_GetBoolValue("AcquisitionFrameRateEnable", stBool)
            if ret != 0:
                print("get acquisition frame rate enable fail! ret[0x%x]" % ret)

            ret = self.obj_cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
            if ret != 0:
                print("set trigger mode fail! ret[0x%x]" % ret)
            return MV_OK

    def Start_grabbing(self, winHandle, table):
        if not self.b_start_grabbing and self.b_open_device:
            self.b_exit = False
            ret = self.obj_cam.MV_CC_StartGrabbing()
            if ret != 0:
                return ret
            self.b_start_grabbing = True
            print("start grabbing successfully!")
            try:
                thread_id = random.randint(1, 10000)
                self.h_thread_handle = threading.Thread(target=CameraOperation.Work_thread, args=(self, winHandle, table))
                self.h_thread_handle.start()
                self.b_thread_closed = True
            finally:
                pass
            return MV_OK

        return MV_E_CALLORDER

    def Stop_grabbing(self):
        if self.b_start_grabbing and self.b_open_device:
            if self.b_thread_closed:
                Stop_thread(self.h_thread_handle)
                self.b_thread_closed = False
            ret = self.obj_cam.MV_CC_StopGrabbing()
            if ret != 0:
                return ret
            print("stop grabbing successfully!")
            self.b_start_grabbing = False
            self.Running = False
            self.b_exit = True
            return MV_OK
        else:
            return MV_E_CALLORDER

    def Close_device(self):
        if self.b_open_device:
            if self.b_thread_closed:
                Stop_thread(self.h_thread_handle)
                self.b_thread_closed = False
            ret = self.obj_cam.MV_CC_CloseDevice()
            if ret != 0:
                return ret

        self.obj_cam.MV_CC_DestroyHandle()
        self.b_open_device = False
        self.b_start_grabbing = False
        self.b_exit = True
        print("close device successfully!")

        return MV_OK

    def Set_trigger_mode(self, is_trigger_mode):
        if not self.b_open_device:
            return MV_E_CALLORDER

        if not is_trigger_mode:
            ret = self.obj_cam.MV_CC_SetEnumValue("TriggerMode", 0)
            if ret != 0:
                return ret
        else:
            ret = self.obj_cam.MV_CC_SetEnumValue("TriggerMode", 1)
            if ret != 0:
                return ret
            ret = self.obj_cam.MV_CC_SetEnumValue("TriggerSource", 7)
            if ret != 0:
                return ret

        return MV_OK

    def Trigger_once(self):
        if self.b_open_device:
            return self.obj_cam.MV_CC_SetCommandValue("TriggerSoftware")

    def Get_parameter(self):
        if self.b_open_device:
            stFloatParam_FrameRate = MVCC_FLOATVALUE()
            memset(byref(stFloatParam_FrameRate), 0, sizeof(MVCC_FLOATVALUE))
            stFloatParam_exposureTime = MVCC_FLOATVALUE()
            memset(byref(stFloatParam_exposureTime), 0, sizeof(MVCC_FLOATVALUE))
            stFloatParam_gain = MVCC_FLOATVALUE()
            memset(byref(stFloatParam_gain), 0, sizeof(MVCC_FLOATVALUE))
            ret = self.obj_cam.MV_CC_GetFloatValue("AcquisitionFrameRate", stFloatParam_FrameRate)
            if ret != 0:
                return ret
            self.frame_rate = stFloatParam_FrameRate.fCurValue

            ret = self.obj_cam.MV_CC_GetFloatValue("ExposureTime", stFloatParam_exposureTime)
            if ret != 0:
                return ret
            self.exposure_time = stFloatParam_exposureTime.fCurValue

            ret = self.obj_cam.MV_CC_GetFloatValue("Gain", stFloatParam_gain)
            if ret != 0:
                return ret
            self.gain = stFloatParam_gain.fCurValue

            return MV_OK

    def Set_parameter(self, frameRate, exposureTime, gain):
        if '' == frameRate or '' == exposureTime or '' == gain:
            print('show info', 'please type in the text box !')
            return MV_E_PARAMETER
        if self.b_open_device:
            ret = self.obj_cam.MV_CC_SetEnumValue("ExposureAuto", 0)
            time.sleep(0.2)
            ret = self.obj_cam.MV_CC_SetFloatValue("ExposureTime", float(exposureTime))
            if ret != 0:
                print('show error', 'set exposure time fail! ret = ' + To_hex_str(ret))
                return ret

            ret = self.obj_cam.MV_CC_SetFloatValue("Gain", float(gain))
            if ret != 0:
                print('show error', 'set gain fail! ret = ' + To_hex_str(ret))
                return ret

            ret = self.obj_cam.MV_CC_SetFloatValue("AcquisitionFrameRate", float(frameRate))
            if ret != 0:
                print('show error', 'set acquistion frame rate fail! ret = ' + To_hex_str(ret))
                return ret

            print('show info', 'set parameter success!')

            return MV_OK
 
    def detect_with_yolo(self, image):
        results = self.model.predict(image, conf=0.9)[0]
        box = results.boxes.xyxy.tolist()

        return box

    def draw_box(self, image, box, table):
        line_position = 300  
        line_color = (0, 255, 255) 
        line_thickness = 2
        object_count = 0 
        current_objects = []
        qrcode = []

        for i, single_box in enumerate(box):
            x1, y1, x2, y2 = map(int, single_box)
            center_x = (x1 + x2) // 2 
            center_y = (y1 + y2) // 2 
            object_count += 1  
            current_objects.append((center_x, center_y))
            if i == 0:  
                self.send_coordinates(center_x, center_y)
            
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.circle(image, (center_x, center_y), 5, (0, 0, 255), -1)
            cv2.putText(image, f"Center: {center_x}, {center_y}", (x1, y2 + 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

            cropped_image = image[y1:y2, x1:x2]
            barcode_data = self.read_barcode(cropped_image)
            if barcode_data:
                qrcode.append(barcode_data)
                print(f"___Barcode data: {barcode_data}")
                cv2.putText(image, f"Barcode: {barcode_data}", (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)
                self.code_signal.emit(str(barcode_data))
                time.sleep(0.1)


        cv2.line(image, (0, line_position), (image.shape[1], line_position), line_color, line_thickness)
        cv2.putText(image, f"Count: {object_count}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
        self.update_table(current_objects, qrcode, table)

        return image, current_objects
    
    def send_coordinates(self, center_x=None, center_y=None):
        if center_x is not None and center_y is not None:
            self.coordinates_signal.emit(center_x, center_y)
            time.sleep(0.1)
        else:
            print("Không có tọa độ mới để phát.")
        
    def read_barcode(self, image):
        barcodes = decode(image)
        if barcodes:
            barcode = barcodes[0]
            barcode_data = barcode.data.decode('utf-8')
            rect_points = barcode.polygon
            if len(rect_points) == 4:
                pts = rect_points
            else:
                pts = cv2.convexHull(np.array([point for point in rect_points], dtype=np.float32))
            cv2.polylines(image, [np.int32(pts)], True, (0, 255, 255), 2)
            return barcode_data
        return None


    def update_table(self, current_objects, qrcode, table):

        current_row_count = table.rowCount()
        print("current_box", current_objects)
        if len(current_objects) != current_row_count:
            table.setRowCount(len(current_objects))
            table.setColumnWidth(3, 220)
            for i, (x, y) in enumerate(current_objects):
                x = QTableWidgetItem(str(x))
                x.setTextAlignment(Qt.AlignCenter)
                y = QTableWidgetItem(str(y))
                y.setTextAlignment(Qt.AlignCenter)
                z = QTableWidgetItem("400")
                z.setTextAlignment(Qt.AlignCenter)
                table.setItem(i, 0, QTableWidgetItem(x))
                table.setItem(i, 1, QTableWidgetItem(y))
                table.setItem(i, 2, QTableWidgetItem(z))
                table.setItem(i, 3, QTableWidgetItem(str(qrcode) if qrcode else "None"))
                

    def Work_thread(self, winHandle, table):
        stOutFrame = MV_FRAME_OUT()
        memset(byref(stOutFrame), 0, sizeof(stOutFrame))

        while True:
            ret = self.obj_cam.MV_CC_GetImageBuffer(stOutFrame, 1000)
            if 0 == ret:
                if self.buf_save_image is None:
                    self.buf_save_image = (c_ubyte * stOutFrame.stFrameInfo.nFrameLen)()
                self.st_frame_info = stOutFrame.stFrameInfo

                self.buf_lock.acquire()
                cdll.msvcrt.memcpy(byref(self.buf_save_image), stOutFrame.pBufAddr, self.st_frame_info.nFrameLen)
                self.buf_lock.release()

                print("get one frame: Width[%d], Height[%d], nFrameNum[%d]"
                      % (self.st_frame_info.nWidth, self.st_frame_info.nHeight, self.st_frame_info.nFrameNum))
                
                image = np.frombuffer(self.buf_save_image, dtype=np.uint8)
                image = image.reshape(1080, 1440)
                image = cv2.cvtColor(image, cv2.COLOR_BAYER_RG2RGB)
                self.obj_cam.MV_CC_FreeImageBuffer(stOutFrame)
                box = self.detect_with_yolo(image)
                img, current_objects = self.draw_box(image, box, table)

                self.convert_cv_to_qt(img, winHandle)
            else:
                print("no data, ret = " + To_hex_str(ret))
                continue

            # Display
            # stDisplayParam = MV_DISPLAY_FRAME_INFO()
            # memset(byref(stDisplayParam), 0, sizeof(stDisplayParam))
            # stDisplayParam.hWnd = int(winHandle)
            # stDisplayParam.nWidth = self.st_frame_info.nWidth
            # stDisplayParam.nHeight = self.st_frame_info.nHeight
            # stDisplayParam.enPixelType = self.st_frame_info.enPixelType
            # stDisplayParam.pData = self.buf_save_image
            # stDisplayParam.nDataLen = self.st_frame_info.nFrameLen
            # self.obj_cam.MV_CC_DisplayOneFrame(stDisplayParam)

            if self.b_exit:
                if self.buf_save_image is not None:
                    del self.buf_save_image
                break
    

    def convert_cv_to_qt(self, cv_img, window):
        label_width = window.width()
        label_height = window.height()
        cv_img = cv2.resize(cv_img, (label_width, label_height))
        height, width, channel = cv_img.shape
        bytes_per_line = channel * width
        cv_img_rgb = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
        qt_img = QImage(cv_img_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(qt_img)
        window.setPixmap(pixmap)

    def Save_jpg(self):

        if self.buf_save_image is None:
            return

        self.buf_lock.acquire()
        file_path = str(self.st_frame_info.nFrameNum) + ".jpg"
        c_file_path = file_path.encode('ascii')
        stSaveParam = MV_SAVE_IMAGE_TO_FILE_PARAM_EX()
        stSaveParam.enPixelType = self.st_frame_info.enPixelType 
        stSaveParam.nWidth = self.st_frame_info.nWidth  
        stSaveParam.nHeight = self.st_frame_info.nHeight 
        stSaveParam.nDataLen = self.st_frame_info.nFrameLen
        stSaveParam.pData = cast(self.buf_save_image, POINTER(c_ubyte))
        stSaveParam.enImageType = MV_Image_Jpeg
        stSaveParam.nQuality = 80
        stSaveParam.pcImagePath = ctypes.create_string_buffer(c_file_path)
        stSaveParam.iMethodValue = 2
        ret = self.obj_cam.MV_CC_SaveImageToFileEx(stSaveParam)
        self.buf_lock.release()
        return ret

    def Save_Bmp(self):

        if 0 == self.buf_save_image:
            return

        self.buf_lock.acquire()
        file_path = str(self.st_frame_info.nFrameNum) + ".bmp"
        c_file_path = file_path.encode('ascii')

        stSaveParam = MV_SAVE_IMAGE_TO_FILE_PARAM_EX()
        stSaveParam.enPixelType = self.st_frame_info.enPixelType
        stSaveParam.nWidth = self.st_frame_info.nWidth
        stSaveParam.nHeight = self.st_frame_info.nHeight
        stSaveParam.nDataLen = self.st_frame_info.nFrameLen
        stSaveParam.pData = cast(self.buf_save_image, POINTER(c_ubyte))
        stSaveParam.enImageType = MV_Image_Bmp
        stSaveParam.nQuality = 8
        stSaveParam.pcImagePath = ctypes.create_string_buffer(c_file_path)
        stSaveParam.iMethodValue = 2
        ret = self.obj_cam.MV_CC_SaveImageToFileEx(stSaveParam)
        self.buf_lock.release()

        return ret

####################*****************#######################
class PLC_Thread(QThread):
    connected = pyqtSignal(bool)
    error = pyqtSignal(str)
    update_plc_data_signal = pyqtSignal(float, float, float, float, float, float, float, float, int, int, int, int)

    def __init__(self):
        super().__init__()
        self.plc_lock = Lock()
        self.plc = plc
        self.plc_connected = False
        self.Running = False
        self.last_cX = None
        self.last_cY = None
        
    def run(self):
        self.Running = True
        while self.Running:
            self.connectPLC()
            self.read_plc_data()
            self.START_Mode()
            self.STOP_Mode()
            self.RESET_Mode()
            self.HOME_Mode()
            time.sleep(0.1)
            pass
    
    def display_first_box(self, center_x, center_y):
        print(f"Receive signal: {center_x}, {center_y}")
        self.last_cX, self.last_cY = self.write_coordinates_to_plc(center_x, center_y, self.last_cX, self.last_cY)
    
    def display_second_code(self, code):
        print(f"Receive signal: {code}\n")
        if code == "https://qrco.de/bfZ6oj/01/00000011142024":
            self.write_number2_to_plc()
            print(f"Number to plc: 2")
        
        if code == "https://qrco.de/bfZ6mR/01/00000014112024":
            self.write_number3_to_plc()
            print(f"Number to plc: 3")
        
    def send_input_to_plc(self, data_x, data_y, data_z):
        try:
            with self.plc_lock:
                if self.plc.get_connected():
                    reading = bytearray(6)
                    snap7.util.set_int(reading, 0, data_x)
                    snap7.util.set_int(reading, 2, data_y)
                    snap7.util.set_int(reading, 4, data_z)
                    self.plc.db_write(1, 90, reading)
                    print(f"Sending input to PLC: {data_x}, {data_y}, {data_z}")
                    time.sleep(0.2)
                else:
                    print("PLC is not connected.")
        except Exception as e:
            print(f"Error sending input to PLC: {e}")
            
    def write_coordinates_to_plc(self, cX, cY, last_cX=None, last_cY=None):
        try:
            with self.plc_lock:
                if self.plc.get_connected():
                    if cX != last_cX or cY != last_cY:
                        reading = bytearray(4)
                        snap7.util.set_int(reading, 0, cX)
                        snap7.util.set_int(reading, 2, cY)
                        
                        self.plc.db_write(1, 84, reading)
                        print(f"Coordinates updated: cX={cX}, cY={cY}")
                        time.sleep(0.2)
                        return cX, cY
                    else:
                        print("No change in coordinates. Skipping write.")
        except Exception as e:
            print(f"Error writing coordinates to PLC: {e}")
        return last_cX, last_cY

    def write_number2_to_plc(self):
        try:
            with self.plc_lock:
                if self.plc.get_connected():
                    reading = bytearray(2)
                    snap7.util.set_int(reading, 0, 2)
                    self.plc.db_write(1, 88, reading)
                    time.sleep(0.2)
        except Exception as e:
            print(f"Error writing number to PLC: {e}")
    
    def write_number3_to_plc(self):
        try:
            with self.plc_lock:
                if self.plc.get_connected():
                    reading = bytearray(2)
                    snap7.util.set_int(reading, 0, 3)
                    self.plc.db_write(1, 88, reading)
                    time.sleep(0.2)
        except Exception as e:
            print(f"Error writing number to PLC: {e}")

    def connectPLC(self):
        try:
            self.plc.connect(ip, rack, slot)
            if self.plc.get_connected():
                print("Connected to PLC Successfully!")
                self.plc_connected = True
                self.connected.emit(True)
            else:
                print("Connect PLC Failed!")
                self.connected.emit(False)
        except Exception as e:
            print(f"Connect PLC Failed! Error: {e}")
            self.error.emit(f"Connect PLC Failed! Error: {e}")

    def disconnectPLC(self):
        try:
            with self.plc_lock:
                if self.plc_connected == True:
                    self.plc.disconnect()
                    print("Disconnected from PLC successfully.")
                    self.plc_connected = False
                    self.connected.emit(False)
                else:
                    print("PLC is already disconnected.")
                    self.connected.emit(False)
        except Exception as e:
            print(f"Disconnect PLC Failed! Error: {e}")
            self.error.emit(f"Disconnect PLC Failed! Error: {e}")
    
    def read_plc_data(self):
        while self.plc_connected == True:
            try:
                with self.plc_lock:
                    delta_x = get_real(plc.db_read(1, 52, 4), 0)
                    delta_y = get_real(plc.db_read(1, 56, 4), 0)
                    delta_z = get_real(plc.db_read(1, 60, 4), 0)
                    axis_x = get_real(plc.db_read(1, 64, 4), 0)
                    axis_y = get_real(plc.db_read(1, 68, 4), 0)
                    axis_z = get_real(plc.db_read(1, 72, 4), 0)
                    tbTocdoBT = get_real(plc.db_read(1, 76, 4), 0)
                    tbTocdoRB = get_real(plc.db_read(1, 80, 4), 0)
                    Pos_X = get_int(plc.db_read(1, 84, 2), 0)
                    Pos_Y = get_int(plc.db_read(1, 86, 2), 0)
                    SLuong = get_int(plc.db_read(1, 46, 2), 0)
                    number = get_int(plc.db_read(1, 88, 2), 0)
                    self.update_plc_data_signal.emit(delta_x, delta_y, delta_z, axis_x, axis_y, axis_z, tbTocdoBT, tbTocdoRB, Pos_X, Pos_Y, SLuong, number)
                    time.sleep(0.4)
            except Exception as e:
                if "CLI : Job pending" in str(e):
                    time.sleep(0.5)
                else:
                    print(f"Error reading PLC data: {e}")
                    self.error.emit(f"Error reading PLC data: {e}")
    
    def START_Mode(self):
        try:
            if self.plc_connected == True:
                db_number = 1
                byte_offset = 18
                bit_position = 1

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Start signal sent. Bit 18.1 is now ON.")
                        else:
                            print("Bit 18.1 is already ON. No action taken.")

                        break

                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(1)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")

        except Exception as e:
            print(f"Error sending start signal: {e}")
            self.error.emit(f"Error sending start signal: {e}")

    def STOP_Mode(self):
        try:
            if self.plc_connected == True:
                db_number = 1
                byte_offset = 18
                bit_position = 2 

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Stop signal sent. Bit 18.2 is now ON.")
                        else:
                            print("Bit 18.2 is already ON. No action taken.")
                        break
                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(1)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
        except Exception as e:
            print(f"Error sending stop signal: {e}")
            self.error.emit(f"Error sending stop signal: {e}")

    def RESET_Mode(self):
        try:
            if self.plc_connected == True:
                db_number = 1
                byte_offset = 18
                bit_position = 3 

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Reset signal sent. Bit 18.3 is now ON.")
                        else:
                            print("Bit 18.3 is already ON. No action taken.")
                        break
                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(1)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
        except Exception as e:
            print(f"Error sending reset signal: {e}")
            self.error.emit(f"Error sending reset signal: {e}")

    def HOME_Mode(self):
        try:
            if self.plc_connected == True:
                db_number = 1
                byte_offset = 18
                bit_position = 4

                for attempt in range(3):
                    try:
                        db_data = self.plc.db_read(db_number, byte_offset, 1)
                        current_byte = bytearray(db_data)[0]
                        is_bit_on = (current_byte & (1 << bit_position)) != 0

                        if not is_bit_on:
                            new_byte = current_byte | (1 << bit_position)
                            self.plc.db_write(db_number, byte_offset, bytearray([new_byte]))
                            print("Home signal sent. Bit 18.4 is now ON.")
                        else:
                            print("Bit 18.4 is already ON. No action taken.")
                        break
                    except RuntimeError as e:
                        if "Job pending" in str(e):
                            print(f"PLC busy, retrying... (Attempt {attempt + 1}/3)")
                            time.sleep(1)
                        else:
                            raise
            else:
                print("PLC is not connected. Please check the connection.")
                self.error.emit("PLC is not connected. Please check the connection.")
        except Exception as e:
            print(f"Error sending home signal: {e}")
            self.error.emit(f"Error sending home signal: {e}")
    
    def stop(self):
        self.disconnectPLC()
        self.Running = False

####################*****************#######################
class MainWindow(QMainWindow):
    data_input_signal = pyqtSignal(int, int, int)
    def __init__(self):
        super().__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        self.plc_controller = PLC_Thread()
        self.camera_controller = CameraOperation(obj_cam = None, st_device_list = None) 

        self.setWindowTitle("Vision and Delta Control System")
        self.setWindowIcon(QIcon(":/icon/eye.png"))

        self.data_input_signal.connect(self.plc_controller.send_input_to_plc)
        
        global deviceList
        deviceList = MV_CC_DEVICE_INFO_LIST()
        global cam
        cam = MvCamera()
        global nSelCamIndex
        nSelCamIndex = 0
        global obj_cam_operation
        obj_cam_operation = 0
        global isOpen
        isOpen = False
        global isGrabbing
        isGrabbing = False
        global isCalibMode 
        isCalibMode = True
        global checkin_programPLC 
        checkin_programPLC = False


        self.ui.slide_menu_container.hide()
        self.ui.stackedWidget.setCurrentIndex(0)

        self.ui.btn_menu.setChecked(True)
        self.ui.btn_Home.clicked.connect(self.on_btn_Home_clicked)
        self.ui.btn_Manual.clicked.connect(self.on_btn_Manual_clicked)
        self.ui.btn_Setting.clicked.connect(self.on_btn_Setting_clicked)
        self.ui.btn_User.clicked.connect(self.on_btn_User_clicked)
        self.ui.btn_exit.clicked.connect(self.on_btn_exit_clicked)
        self.ui.btn_menu.clicked.connect(self.on_btn_menu_clicked)

        self.ui.btn_Scan.clicked.connect(self.enum_devices)
        self.ui.bnOpen.clicked.connect(self.open_device)
        self.ui.bnStart.clicked.connect(self.start_grabbing)
        self.ui.bnClose.clicked.connect(self.close_device)
        self.ui.btn_ConnectCamera.clicked.connect(self.start_camera)
        self.ui.bnStop.clicked.connect(self.stop_grabbing)
        self.ui.btn_RUN.clicked.connect(self.print_input_data)

        self.ui.btn_ConnectPLC.clicked.connect(self.start_plc_thread)
        self.ui.btn_START.clicked.connect(self.plc_controller.START_Mode)
        self.ui.btn_START_2.clicked.connect(self.plc_controller.START_Mode)
        self.ui.btn_STOP.clicked.connect(self.plc_controller.STOP_Mode)
        self.ui.btn_STOP_2.clicked.connect(self.plc_controller.STOP_Mode)
        self.ui.btn_RESET.clicked.connect(self.plc_controller.RESET_Mode)
        self.ui.btn_RESET_2.clicked.connect(self.plc_controller.RESET_Mode)
        self.ui.btn_HOME.clicked.connect(self.plc_controller.HOME_Mode)
        self.ui.btn_HOME_2.clicked.connect(self.plc_controller.HOME_Mode)

        self.ui.bnSoftwareTrigger.clicked.connect(self.trigger_once)
        self.ui.radioTriggerMode.clicked.connect(self.set_software_trigger_mode)
        self.ui.radioContinueMode.clicked.connect(self.set_continue_mode)

        self.ui.bnGetParam.clicked.connect(self.get_param)
        self.ui.bnSetParam.clicked.connect(self.set_param)
        self.ui.bnSaveImage.clicked.connect(self.save_bmp)


        
    def xFunc(self, event):
        global nSelCamIndex
        nSelCamIndex = TxtWrapBy("[", "]", self.ui.ComboDevices.get())

    def decoding_char(self, c_ubyte_value):
        c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
        try:
            decode_str = c_char_p_value.value.decode('gbk') 
        except UnicodeDecodeError:
            decode_str = str(c_char_p_value.value)
        return decode_str
    
    def enum_devices(self):
        global deviceList
        global obj_cam_operation

        deviceList = MV_CC_DEVICE_INFO_LIST()
        ret = MvCamera.MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, deviceList)
        if ret != 0:
            strError = "Enum devices fail! ret = :" + ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(self, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print("Find %d devices!" % deviceList.nDeviceNum)

        devList = []
        for i in range(0, deviceList.nDeviceNum):
            mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
            if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE:
                print("\ngige device: [%d]" % i)
                user_defined_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                model_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                print("device user define name: " + user_defined_name)
                print("device model name: " + model_name)

                nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))
                self.ui.lb_ipcamera.setText(str(nip1) + "." + str(nip2) + "." + str(nip3) + "." + str(nip4))
                devList.append(
                    "[" + str(i) + "]GigE: " + user_defined_name + " " + model_name + "(" + str(nip1) + "." + str(
                        nip2) + "." + str(nip3) + "." + str(nip4) + ")")
            elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                print("\nu3v device: [%d]" % i)
                user_defined_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                model_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                print("device user define name: " + user_defined_name)
                print("device model name: " + model_name)

                strSerialNumber = ""
                for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                    if per == 0:
                        break
                    strSerialNumber = strSerialNumber + chr(per)
                print("user serial number: " + strSerialNumber)
                devList.append("[" + str(i) + "]USB: " + user_defined_name + " " + model_name
                               + "(" + str(strSerialNumber) + ")")

        self.ui.ComboDevices.clear()
        self.ui.ComboDevices.addItems(devList)
        self.ui.ComboDevices.setCurrentIndex(0)
    
    def open_device(self):
        global deviceList
        global nSelCamIndex
        global obj_cam_operation
        global isOpen

        if isOpen:
            QMessageBox.warning(self, "Error", 'Camera is Running!', QMessageBox.Ok)
            return MV_E_CALLORDER

        nSelCamIndex = self.ui.ComboDevices.currentIndex()
        if nSelCamIndex < 0:
            QMessageBox.warning(self, "Error", 'Please select a camera!', QMessageBox.Ok)
            return MV_E_CALLORDER

        obj_cam_operation = CameraOperation(cam, deviceList, nSelCamIndex)
        ret = obj_cam_operation.Open_device()
        if 0 != ret:
            strError = "Open device failed ret:" + ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
            isOpen = False
        else:
            self.set_continue_mode()
            self.get_param()
            isOpen = True
            self.ui.label_text.setText("Open device successfully!")
            self.enable_controls()
    
    def start_grabbing(self):
        global obj_cam_operation
        global isGrabbing

        ret = obj_cam_operation.Start_grabbing(self.ui.widgetDisplay, self.ui.tableWidget_out)
        if ret != 0:
            strError = "Start grabbing failed ret:" + ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            isGrabbing = True
            self.ui.label_text.setText("Start grabbing successfully!")
            self.enable_controls()
    
    def start_camera(self):
        global isGrabbing 
        isGrabbing = True 

        print ("Start all")
        self.enum_devices()
        self.open_device()
        self.start_grabbing()
        self.start_cam_thread()

    def stop_grabbing(self):
        global obj_cam_operation
        global isGrabbing

        ret = obj_cam_operation.Stop_grabbing()
        if ret != 0:
            strError = "Stop grabbing failed ret:" + ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            isGrabbing = False
            self.ui.label_text.setText("Stop grabbing successfully!")
            self.enable_controls()
    
    def close_device(self):
        global isOpen
        global isGrabbing
        global obj_cam_operation

        if isOpen:
            obj_cam_operation.Close_device()
            isOpen = False

        isGrabbing = False
        self.enable_controls()
    
    def set_continue_mode(self):
        strError = None
        trigger_mode_enabled = False

        ret = obj_cam_operation.Set_trigger_mode(False)
        if ret != 0:
            strError = "Set continue mode failed ret:" + ToHexStr(ret) + " mode is " + str(trigger_mode_enabled)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            self.ui.radioContinueMode.setChecked(True)
            self.ui.radioTriggerMode.setChecked(False)
            self.ui.bnSoftwareTrigger.setEnabled(False)
    
    def set_software_trigger_mode(self):

        ret = obj_cam_operation.Set_trigger_mode(True)
        if ret != 0:
            strError = "Set trigger mode failed ret:" + ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            self.ui.radioContinueMode.setChecked(False)
            self.ui.radioTriggerMode.setChecked(True)
            self.ui.bnSoftwareTrigger.setEnabled(isGrabbing)

    def trigger_once(self):
        ret = obj_cam_operation.Trigger_once()
        if ret != 0:
            strError = "TriggerSoftware failed ret:" + ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
    
    def save_bmp(self):
        ret = obj_cam_operation.Save_Bmp()
        if ret != MV_OK:
            strError = "Save BMP failed ret:" + ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            print("Save image success")
            
    def is_float(str):
        try:
            float(str)
            return True
        except ValueError:
            return False
    
    def get_param(self):
        ret = obj_cam_operation.Get_parameter()
        if ret != MV_OK:
            strError = "Get param failed ret:" + ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            self.ui.edtExposureTime.setText("{0:.2f}".format(obj_cam_operation.exposure_time))
            self.ui.edtGain.setText("{0:.2f}".format(obj_cam_operation.gain))
            self.ui.edtFrameRate.setText("{0:.2f}".format(obj_cam_operation.frame_rate))

    def set_param(self):
        frame_rate = self.ui.edtFrameRate.text()
        exposure = self.ui.edtExposureTime.text()
        gain = self.ui.edtGain.text()

        if self.is_float(frame_rate)!=True or self.is_float(exposure)!=True or self.is_float(gain)!=True:
            strError = "Set param failed ret:" + ToHexStr(MV_E_PARAMETER)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
            return MV_E_PARAMETER
        
        ret = obj_cam_operation.Set_parameter(frame_rate, exposure, gain)
        if ret != MV_OK:
            strError = "Set param failed ret:" + ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)

        return MV_OK

    def enable_controls(self):
        global isGrabbing
        global isOpen

        self.ui.bnOpen.setEnabled(not isOpen)
        self.ui.bnClose.setEnabled(isOpen)
        self.ui.btn_ConnectCamera.setEnabled(isOpen and (not isGrabbing))
        self.ui.bnStop.setEnabled(isOpen and isGrabbing)
        self.ui.bnSoftwareTrigger.setEnabled(isGrabbing and self.ui.radioTriggerMode.isChecked())
        self.ui.bnSaveImage.setEnabled(isOpen and isGrabbing)
    
    def on_btn_menu_clicked(self, checked):
        if checked:
            self.ui.slide_menu_container.show()
        else:
            self.ui.slide_menu_container.hide()
    def on_btn_Home_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(0)
    def on_btn_Manual_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(1)
    def on_btn_Setting_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(2)
    def on_btn_User_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(3)
    
    def on_btn_exit_clicked(self):
        self.close()
        self.close_plc()
        self.close_device()
        print("Exit Program")
        sys.exit()
    
    def closeEvent(self, event):
        reply = QMessageBox.question(self, "Exit", "Are you sure to exit?",
                                 QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
    
    def close_plc(self):
        self.plc_controller.stop()
        self.plc_controller.terminate()
        self.plc_controller.wait()

    def start_cam_thread(self):
        self.camera_controller.my_signal.connect(self.handle_message)
        self.camera_controller.start()

    def handle_message(self, message):
        print(f"Receiver: Nhận được tín hiệu: {message}")
    
    def start_plc_thread(self):
        self.plc_controller.update_plc_data_signal.connect(self.update_plc_data)
        self.plc_controller.error.connect(self.Error_PLC)
        self.plc_controller.connected.connect(self.on_plc_connected)
        self.plc_controller.start()

    def on_plc_connected(self, connected):
        if connected:
            self.ui.label_8.setText("PLC Siemens S7-1200")
            self.ui.label_11.setText(ip)
            self.ui.label_12.setText("Connected to PLC Successfully!")
            self.ui.system_active.setStyleSheet("background-color: green; color: white; font-size: 16px;")
            self.ui.system_active.setText("System: ACTIVE")
        else:
            QMessageBox.warning(self,"PLC Connection", "Failed to connect to PLC.")
            self.ui.label_12.setText("Failed to connect to PLC.")
            self.ui.system_active.setStyleSheet("background-color: red; color: white; font-size: 16px;")
            self.ui.system_active.setText("System: INACTIVE")

    @pyqtSlot(float, float, float, float, float, float, float, float, int, int, int, int)
    def update_plc_data(self, delta_x, delta_y, delta_z, axis_x, axis_y, axis_z, tbTocdoBT, tbTocdoRB, Pos_X, Pos_Y, SLuong, number):
        self.ui.tbThetaX.setText(f"{delta_x:.2f}")
        self.ui.tbThetaY.setText(f"{delta_y:.2f}")
        self.ui.tbThetaZ.setText(f"{delta_z:.2f}")
        self.ui.tbAxisX.setText(f"{axis_x:.2f}")
        self.ui.tbAxisY.setText(f"{axis_y:.2f}")
        self.ui.tbAxisZ.setText(f"{axis_z:.2f}")
        self.ui.lTocdoBT.setText(f"{tbTocdoBT:.2f}")
        self.ui.lTocdoRB.setText(f"{tbTocdoRB:.2f}")
        self.ui.Pos_X.setText(str(Pos_X))
        self.ui.Pos_Y.setText(str(Pos_Y))
        self.ui.label_SL.setText(str(SLuong))
        self.ui.label_loaiSP.setText(str(number))
    
    def print_input_data(self):
        try:
            data_X = int(self.ui.lineEdit_TD_X.text().strip())
            data_Y = int(self.ui.lineEdit_TD_Y.text().strip())
            data_Z = int(self.ui.lineEdit_TD_Z.text().strip())
            
            if 10 <= data_X <= 100 and 10 <= data_Y <= 100 and -450 <= data_Z <= -350:
                print(f"Nhập tọa độ X: {data_X}")
                print(f"Nhập tọa độ Y: {data_Y}")
                print(f"Nhập tọa độ Z: {data_Z}")
                self.data_input_signal.emit(int(data_X), int(data_Y), int(data_Z))
            else:
                QMessageBox.warning(self, "Error", "Data must be within the allowed range.")
        except Exception as e:
            print(f"Error: {e}")
            QMessageBox.warning(self, "Error", "Please enter a valid number.")


    def Error_PLC(self, error):
        QMessageBox.warning(self, "PLC Error", error)
        self.ui.label_error.setText(error)

def main():
    app = QApplication(sys.argv)
    mainWindow = MainWindow()
    mainWindow.show()
    sys.exit(app.exec_())
        
if __name__ == '__main__':
    main()