<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1400</width>
    <height>900</height>
   </size>
  </property>
  <property name="font">
   <font>
    <family>Times New Roman</family>
    <pointsize>10</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="styleSheet">
    <string notr="true">background-color: rgb(24, 24, 36);</string>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QFrame" name="slide_menu_container">
      <property name="maximumSize">
       <size>
        <width>200</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item alignment="Qt::AlignTop">
        <widget class="QFrame" name="frame_2">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_5">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="logo_main">
            <property name="font">
             <font>
              <pointsize>18</pointsize>
              <weight>75</weight>
              <italic>true</italic>
              <bold>true</bold>
              <stylestrategy>PreferAntialias</stylestrategy>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 rgba(0, 0, 0, 255), stop:0.33 rgba(0, 0, 0, 255), stop:0.34 rgba(255, 30, 30, 255), stop:0.66 rgba(255, 0, 0, 255), stop:0.67 rgba(255, 255, 0, 255), stop:1 rgba(255, 255, 0, 255));</string>
            </property>
            <property name="text">
             <string>Delta Robot</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="frame_3">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_6">
          <property name="spacing">
           <number>5</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="btn_Home">
            <property name="font">
             <font>
              <family>Times New Roman</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LeftToRight</enum>
            </property>
            <property name="styleSheet">
             <string notr="true">#btn_Home {
    padding-left: 10px;
    text-align: left;
	color: rgb(85, 255, 255);
}
#btn_Home:hover{
	background-color: rgb(66, 66, 99);
}
#btn_Home:pressed{
	border-color: rgb(66, 66, 99);
}
#btn_Home:active {
    background-color: rgb(24, 24, 36);
}
#btn_Home:focus {
    background-color: rgb(66, 66, 99); /* Khôi phục màu mặc định sau khi nhấn */
    outline: none; /* Xóa viền focus */
}</string>
            </property>
            <property name="text">
             <string> Home</string>
            </property>
            <property name="icon">
             <iconset resource="icon.qrc">
              <normaloff>:/Icons/Icons/home.svg</normaloff>:/Icons/Icons/home.svg</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="autoExclusive">
             <bool>true</bool>
            </property>
            <property name="autoRepeatDelay">
             <number>300</number>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_Manual">
            <property name="font">
             <font>
              <family>Times New Roman</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">#btn_Manual {
    padding-left: 10px;
    text-align: left;
	color: rgb(85, 255, 255);
}
#btn_Manual:hover{
    background-color: rgb(66, 66, 99);
}
#btn_Manual:pressed{
    border-color: rgb(66, 66, 99);
}
#btn_Manual:active {
    background-color: rgb(24, 24, 36);
}
#btn_Manual:focus {
    background-color: rgb(66, 66, 99); /* Khôi phục màu mặc định sau khi nhấn */
    outline: none; /* Xóa viền focus */
}</string>
            </property>
            <property name="text">
             <string> Manual</string>
            </property>
            <property name="icon">
             <iconset resource="icon.qrc">
              <normaloff>:/Icons/Icons/move.svg</normaloff>:/Icons/Icons/move.svg</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="autoExclusive">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_Setting">
            <property name="font">
             <font>
              <family>Times New Roman</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">#btn_Setting {
    padding-left: 10px;
    text-align: left;
	color: rgb(85, 255, 255);
} 
#btn_Setting:hover{
    background-color: rgb(66, 66, 99);
}
#btn_Setting:pressed{
    border-color: rgb(66, 66, 99);
}
#btn_Setting:active {
    background-color: rgb(24, 24, 36);
}
#btn_Setting:focus {
    background-color: rgb(66, 66, 99); /* Khôi phục màu mặc định sau khi nhấn */
    outline: none; /* Xóa viền focus */
}</string>
            </property>
            <property name="text">
             <string> Setting</string>
            </property>
            <property name="icon">
             <iconset resource="icon.qrc">
              <normaloff>:/Icons/Icons/settings.svg</normaloff>:/Icons/Icons/settings.svg</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="autoExclusive">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btn_User">
            <property name="styleSheet">
             <string notr="true">#btn_User {
    padding-left: 10px;
    text-align: left;
	color: rgb(85, 255, 255);
	background-color: rgb(24, 24, 36);
}
#btn_User:hover{
    background-color: rgb(66, 66, 99);
}
#btn_User:pressed{
    border-color: rgb(66, 66, 99);
}
#btn_User:active {
    background-color: rgb(24, 24, 36);
}
#btn_User:focus {
    background-color: rgb(66, 66, 99); /* Khôi phục màu mặc định sau khi nhấn */
    outline: none; /* Xóa viền focus */
}

</string>
            </property>
            <property name="text">
             <string> User</string>
            </property>
            <property name="icon">
             <iconset resource="icon.qrc">
              <normaloff>:/Icons/Icons/users.svg</normaloff>:/Icons/Icons/users.svg</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="autoExclusive">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item alignment="Qt::AlignBottom">
        <widget class="QFrame" name="frame_4">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_8">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item alignment="Qt::AlignBottom">
           <widget class="QPushButton" name="btn_exit">
            <property name="styleSheet">
             <string notr="true">color: rgb(170, 255, 255);</string>
            </property>
            <property name="text">
             <string>Exit</string>
            </property>
            <property name="icon">
             <iconset resource="icon.qrc">
              <normaloff>:/Icons/Icons/power.svg</normaloff>:/Icons/Icons/power.svg</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="autoExclusive">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="main_body">
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item alignment="Qt::AlignTop">
        <widget class="QFrame" name="header_frame">
         <property name="styleSheet">
          <string notr="true">color: rgb(170, 255, 255);</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="frame_7">
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_6">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QFrame" name="frame">
               <property name="minimumSize">
                <size>
                 <width>65</width>
                 <height>0</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::StyledPanel</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_7">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item alignment="Qt::AlignLeft|Qt::AlignTop">
                 <widget class="QPushButton" name="btn_menu">
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="icon.qrc">
                    <normaloff>:/Icons/Icons/align-left.svg</normaloff>:/Icons/Icons/align-left.svg</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>32</width>
                    <height>32</height>
                   </size>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                  <property name="autoExclusive">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item alignment="Qt::AlignLeft|Qt::AlignVCenter">
              <widget class="QLineEdit" name="search_lineEdit">
               <property name="minimumSize">
                <size>
                 <width>500</width>
                 <height>32</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>Times New Roman</family>
                 <pointsize>10</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(85, 255, 255);
background-color: rgb(24, 24, 36);</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="placeholderText">
                <string>Search.... </string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_search">
               <property name="minimumSize">
                <size>
                 <width>30</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="icon">
                <iconset resource="icon.qrc">
                 <normaloff>:/Icons/Icons/search.svg</normaloff>:/Icons/Icons/search.svg</iconset>
               </property>
               <property name="iconSize">
                <size>
                 <width>32</width>
                 <height>32</height>
                </size>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item alignment="Qt::AlignRight|Qt::AlignTop">
           <widget class="QFrame" name="frame_8">
            <property name="minimumSize">
             <size>
              <width>60</width>
              <height>0</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item alignment="Qt::AlignBottom">
              <widget class="QPushButton" name="btn_admin">
               <property name="text">
                <string/>
               </property>
               <property name="icon">
                <iconset resource="icon.qrc">
                 <normaloff>:/Icons/Icons/user.svg</normaloff>:/Icons/Icons/user.svg</iconset>
               </property>
               <property name="iconSize">
                <size>
                 <width>32</width>
                 <height>32</height>
                </size>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item alignment="Qt::AlignBottom">
              <widget class="QPushButton" name="btn_Notification">
               <property name="text">
                <string/>
               </property>
               <property name="icon">
                <iconset resource="icon.qrc">
                 <normaloff>:/Icons/Icons/bell.svg</normaloff>:/Icons/Icons/bell.svg</iconset>
               </property>
               <property name="iconSize">
                <size>
                 <width>32</width>
                 <height>32</height>
                </size>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="main_body_contents">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>645</width>
           <height>519</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_7">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QStackedWidget" name="stackedWidget">
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="currentIndex">
             <number>1</number>
            </property>
            <widget class="QWidget" name="page">
             <layout class="QVBoxLayout" name="verticalLayout_8">
              <property name="spacing">
               <number>0</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QFrame" name="frame_5">
                <property name="styleSheet">
                 <string notr="true">.frame-gradient {
  border: 5px solid;
  border-image: linear-gradient(to right, #ff7e5f, #feb47b) 1; /* Viền gradient */
}</string>
                </property>
                <property name="frameShape">
                 <enum>QFrame::StyledPanel</enum>
                </property>
                <property name="frameShadow">
                 <enum>QFrame::Raised</enum>
                </property>
                <layout class="QHBoxLayout" name="horizontalLayout_9">
                 <item>
                  <widget class="QFrame" name="frame_15">
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <property name="frameShape">
                    <enum>QFrame::StyledPanel</enum>
                   </property>
                   <property name="frameShadow">
                    <enum>QFrame::Raised</enum>
                   </property>
                   <widget class="QLabel" name="widgetDisplay">
                    <property name="geometry">
                     <rect>
                      <x>0</x>
                      <y>50</y>
                      <width>640</width>
                      <height>480</height>
                     </rect>
                    </property>
                    <property name="text">
                     <string/>
                    </property>
                   </widget>
                   <widget class="QTableWidget" name="tableWidget_out">
                    <property name="geometry">
                     <rect>
                      <x>0</x>
                      <y>560</y>
                      <width>640</width>
                      <height>220</height>
                     </rect>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">background-color: rgb(214, 214, 214);</string>
                    </property>
                    <column>
                     <property name="text">
                      <string>Center X</string>
                     </property>
                     <property name="font">
                      <font>
                       <family>Times New Roman</family>
                       <pointsize>10</pointsize>
                       <weight>75</weight>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="textAlignment">
                      <set>AlignCenter</set>
                     </property>
                    </column>
                    <column>
                     <property name="text">
                      <string>Center Y</string>
                     </property>
                     <property name="font">
                      <font>
                       <family>Times New Roman</family>
                       <pointsize>10</pointsize>
                       <weight>75</weight>
                       <bold>true</bold>
                      </font>
                     </property>
                    </column>
                    <column>
                     <property name="text">
                      <string>Center Z</string>
                     </property>
                     <property name="font">
                      <font>
                       <family>Times New Roman</family>
                       <pointsize>10</pointsize>
                       <weight>75</weight>
                       <bold>true</bold>
                      </font>
                     </property>
                    </column>
                    <column>
                     <property name="text">
                      <string>Status</string>
                     </property>
                     <property name="font">
                      <font>
                       <family>Times New Roman</family>
                       <pointsize>10</pointsize>
                       <weight>75</weight>
                       <bold>true</bold>
                      </font>
                     </property>
                    </column>
                   </widget>
                   <widget class="QLabel" name="label_15">
                    <property name="geometry">
                     <rect>
                      <x>0</x>
                      <y>0</y>
                      <width>200</width>
                      <height>40</height>
                     </rect>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>13</pointsize>
                      <weight>75</weight>
                      <italic>true</italic>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">color: rgb(85, 255, 255);</string>
                    </property>
                    <property name="text">
                     <string>Autorun Detection</string>
                    </property>
                   </widget>
                  </widget>
                 </item>
                 <item>
                  <widget class="QFrame" name="frame_12">
                   <property name="frameShape">
                    <enum>QFrame::StyledPanel</enum>
                   </property>
                   <property name="frameShadow">
                    <enum>QFrame::Raised</enum>
                   </property>
                   <layout class="QHBoxLayout" name="horizontalLayout_10">
                    <property name="spacing">
                     <number>0</number>
                    </property>
                    <property name="leftMargin">
                     <number>0</number>
                    </property>
                    <property name="topMargin">
                     <number>0</number>
                    </property>
                    <property name="rightMargin">
                     <number>0</number>
                    </property>
                    <property name="bottomMargin">
                     <number>0</number>
                    </property>
                    <item>
                     <widget class="QGroupBox" name="Gbox_ConnectDeltaRobot">
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>13</pointsize>
                        <weight>75</weight>
                        <italic>true</italic>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">#Gbox_ConnectDeltaRobot{
	color: rgb(85, 255, 255);
}</string>
                      </property>
                      <property name="title">
                       <string>Connect Delta Robot</string>
                      </property>
                      <widget class="QPushButton" name="btn_START">
                       <property name="geometry">
                        <rect>
                         <x>70</x>
                         <y>650</y>
                         <width>200</width>
                         <height>50</height>
                        </rect>
                       </property>
                       <property name="font">
                        <font>
                         <family>Times New Roman</family>
                         <pointsize>10</pointsize>
                         <weight>75</weight>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">background-color: rgb(85, 255, 127);</string>
                       </property>
                       <property name="text">
                        <string>STRAT</string>
                       </property>
                      </widget>
                      <widget class="QPushButton" name="btn_STOP">
                       <property name="geometry">
                        <rect>
                         <x>70</x>
                         <y>720</y>
                         <width>200</width>
                         <height>50</height>
                        </rect>
                       </property>
                       <property name="font">
                        <font>
                         <family>Times New Roman</family>
                         <pointsize>10</pointsize>
                         <weight>75</weight>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">background-color: rgb(255, 0, 0);</string>
                       </property>
                       <property name="text">
                        <string>STOP</string>
                       </property>
                      </widget>
                      <widget class="QLabel" name="label">
                       <property name="geometry">
                        <rect>
                         <x>10</x>
                         <y>30</y>
                         <width>51</width>
                         <height>30</height>
                        </rect>
                       </property>
                       <property name="font">
                        <font>
                         <family>Times New Roman</family>
                         <pointsize>12</pointsize>
                         <weight>75</weight>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(85, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>PLC</string>
                       </property>
                      </widget>
                      <widget class="QPushButton" name="btn_ConnectPLC">
                       <property name="geometry">
                        <rect>
                         <x>20</x>
                         <y>210</y>
                         <width>560</width>
                         <height>40</height>
                        </rect>
                       </property>
                       <property name="font">
                        <font>
                         <family>Times New Roman</family>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(85, 255, 255);
background-color: rgb(75, 75, 75);</string>
                       </property>
                       <property name="text">
                        <string>Connect  PLC</string>
                       </property>
                       <property name="checkable">
                        <bool>false</bool>
                       </property>
                      </widget>
                      <widget class="QLabel" name="label_2">
                       <property name="geometry">
                        <rect>
                         <x>10</x>
                         <y>260</y>
                         <width>121</width>
                         <height>30</height>
                        </rect>
                       </property>
                       <property name="font">
                        <font>
                         <family>Times New Roman</family>
                         <pointsize>12</pointsize>
                         <weight>75</weight>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(85, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>CAMERA</string>
                       </property>
                      </widget>
                      <widget class="QPushButton" name="btn_ConnectCamera">
                       <property name="geometry">
                        <rect>
                         <x>20</x>
                         <y>590</y>
                         <width>560</width>
                         <height>40</height>
                        </rect>
                       </property>
                       <property name="font">
                        <font>
                         <family>Times New Roman</family>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(85, 255, 255);
background-color: rgb(75, 75, 75);</string>
                       </property>
                       <property name="text">
                        <string>Connect Camera</string>
                       </property>
                      </widget>
                      <widget class="QFrame" name="frame_13">
                       <property name="geometry">
                        <rect>
                         <x>20</x>
                         <y>70</y>
                         <width>560</width>
                         <height>128</height>
                        </rect>
                       </property>
                       <property name="maximumSize">
                        <size>
                         <width>560</width>
                         <height>128</height>
                        </size>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">background-color: rgb(220, 220, 220);</string>
                       </property>
                       <property name="frameShape">
                        <enum>QFrame::StyledPanel</enum>
                       </property>
                       <property name="frameShadow">
                        <enum>QFrame::Raised</enum>
                       </property>
                       <widget class="QLabel" name="label_5">
                        <property name="geometry">
                         <rect>
                          <x>20</x>
                          <y>10</y>
                          <width>80</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Name</string>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_6">
                        <property name="geometry">
                         <rect>
                          <x>20</x>
                          <y>50</y>
                          <width>80</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>IP</string>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_7">
                        <property name="geometry">
                         <rect>
                          <x>20</x>
                          <y>90</y>
                          <width>80</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Text</string>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_8">
                        <property name="geometry">
                         <rect>
                          <x>100</x>
                          <y>10</y>
                          <width>360</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">color: rgb(255, 0, 0);
background-color: rgb(255, 255, 255);
</string>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_11">
                        <property name="geometry">
                         <rect>
                          <x>100</x>
                          <y>50</y>
                          <width>360</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">color: rgb(255, 0, 0);
background-color: rgb(255, 255, 255);
</string>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_12">
                        <property name="geometry">
                         <rect>
                          <x>100</x>
                          <y>90</y>
                          <width>360</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">color: rgb(255, 0, 0);
background-color: rgb(255, 255, 255);
</string>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                       </widget>
                      </widget>
                      <widget class="QFrame" name="frame_14">
                       <property name="geometry">
                        <rect>
                         <x>20</x>
                         <y>290</y>
                         <width>560</width>
                         <height>291</height>
                        </rect>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">background-color: rgb(220, 220, 220);</string>
                       </property>
                       <property name="frameShape">
                        <enum>QFrame::StyledPanel</enum>
                       </property>
                       <property name="frameShadow">
                        <enum>QFrame::Raised</enum>
                       </property>
                       <widget class="QLabel" name="label_3">
                        <property name="geometry">
                         <rect>
                          <x>20</x>
                          <y>10</y>
                          <width>80</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Name</string>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_10">
                        <property name="geometry">
                         <rect>
                          <x>20</x>
                          <y>50</y>
                          <width>80</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>IP</string>
                        </property>
                       </widget>
                       <widget class="QLabel" name="lb_ipcamera">
                        <property name="geometry">
                         <rect>
                          <x>100</x>
                          <y>50</y>
                          <width>360</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">color: rgb(255, 0, 0);
background-color: rgb(255, 255, 255);
</string>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                       </widget>
                       <widget class="QComboBox" name="ComboDevices">
                        <property name="geometry">
                         <rect>
                          <x>100</x>
                          <y>10</y>
                          <width>360</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">color: rgb(255, 0, 0);</string>
                        </property>
                       </widget>
                       <widget class="QPushButton" name="btn_Scan">
                        <property name="geometry">
                         <rect>
                          <x>465</x>
                          <y>10</y>
                          <width>100</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">background-color: rgb(255, 85, 0);
color: rgb(255, 255, 255);</string>
                        </property>
                        <property name="text">
                         <string>Scan</string>
                        </property>
                       </widget>
                       <widget class="QPushButton" name="bnClose">
                        <property name="geometry">
                         <rect>
                          <x>150</x>
                          <y>130</y>
                          <width>100</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">color: rgb(255, 255, 255);
background-color: rgb(255, 0, 0);</string>
                        </property>
                        <property name="text">
                         <string>Disconnect</string>
                        </property>
                       </widget>
                       <widget class="QRadioButton" name="radioContinueMode">
                        <property name="geometry">
                         <rect>
                          <x>30</x>
                          <y>170</y>
                          <width>100</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true"/>
                        </property>
                        <property name="text">
                         <string>Auto</string>
                        </property>
                       </widget>
                       <widget class="QRadioButton" name="radioTriggerMode">
                        <property name="geometry">
                         <rect>
                          <x>160</x>
                          <y>170</y>
                          <width>100</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Trigger</string>
                        </property>
                       </widget>
                       <widget class="QPushButton" name="bnStop">
                        <property name="enabled">
                         <bool>false</bool>
                        </property>
                        <property name="geometry">
                         <rect>
                          <x>30</x>
                          <y>250</y>
                          <width>100</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">background-color: rgb(255, 0, 0);
color: rgb(255, 255, 255);</string>
                        </property>
                        <property name="text">
                         <string>Stop</string>
                        </property>
                       </widget>
                       <widget class="QPushButton" name="bnOpen">
                        <property name="geometry">
                         <rect>
                          <x>30</x>
                          <y>130</y>
                          <width>100</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">background-color: rgb(85, 255, 127);
color: rgb(255, 0, 0);</string>
                        </property>
                        <property name="text">
                         <string>Open</string>
                        </property>
                       </widget>
                       <widget class="QPushButton" name="bnSaveImage">
                        <property name="enabled">
                         <bool>false</bool>
                        </property>
                        <property name="geometry">
                         <rect>
                          <x>150</x>
                          <y>250</y>
                          <width>100</width>
                          <height>28</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">background-color: rgb(170, 255, 255);
color: rgb(255, 0, 0);</string>
                        </property>
                        <property name="text">
                         <string>Save</string>
                        </property>
                       </widget>
                       <widget class="QPushButton" name="bnSoftwareTrigger">
                        <property name="enabled">
                         <bool>false</bool>
                        </property>
                        <property name="geometry">
                         <rect>
                          <x>150</x>
                          <y>210</y>
                          <width>100</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">color: rgb(0, 0, 0);</string>
                        </property>
                        <property name="text">
                         <string>Set Trigger</string>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_4">
                        <property name="geometry">
                         <rect>
                          <x>290</x>
                          <y>130</y>
                          <width>70</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Exposure</string>
                        </property>
                       </widget>
                       <widget class="QLineEdit" name="edtExposureTime">
                        <property name="geometry">
                         <rect>
                          <x>420</x>
                          <y>130</y>
                          <width>140</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="text">
                         <string>0</string>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_13">
                        <property name="geometry">
                         <rect>
                          <x>290</x>
                          <y>170</y>
                          <width>70</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Gain</string>
                        </property>
                       </widget>
                       <widget class="QLineEdit" name="edtGain">
                        <property name="geometry">
                         <rect>
                          <x>420</x>
                          <y>170</y>
                          <width>140</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="text">
                         <string>0</string>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_9">
                        <property name="geometry">
                         <rect>
                          <x>290</x>
                          <y>210</y>
                          <width>81</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Frame rate</string>
                        </property>
                       </widget>
                       <widget class="QLineEdit" name="edtFrameRate">
                        <property name="geometry">
                         <rect>
                          <x>420</x>
                          <y>210</y>
                          <width>140</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="text">
                         <string>0</string>
                        </property>
                       </widget>
                       <widget class="QPushButton" name="bnGetParam">
                        <property name="geometry">
                         <rect>
                          <x>290</x>
                          <y>250</y>
                          <width>121</width>
                          <height>28</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Get Parameters</string>
                        </property>
                       </widget>
                       <widget class="QPushButton" name="bnSetParam">
                        <property name="geometry">
                         <rect>
                          <x>420</x>
                          <y>250</y>
                          <width>140</width>
                          <height>28</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Setting Parameters</string>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_text">
                        <property name="geometry">
                         <rect>
                          <x>100</x>
                          <y>90</y>
                          <width>360</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">color: rgb(255, 0, 0);
background-color: rgb(255, 255, 255);
</string>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                       </widget>
                       <widget class="QLabel" name="label_14">
                        <property name="geometry">
                         <rect>
                          <x>20</x>
                          <y>90</y>
                          <width>80</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Text</string>
                        </property>
                       </widget>
                       <widget class="QPushButton" name="bnStart">
                        <property name="geometry">
                         <rect>
                          <x>30</x>
                          <y>210</y>
                          <width>100</width>
                          <height>30</height>
                         </rect>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>10</pointsize>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">background-color: rgb(85, 170, 127);
color: rgb(255, 255, 255);</string>
                        </property>
                        <property name="text">
                         <string>Start</string>
                        </property>
                       </widget>
                      </widget>
                      <widget class="QPushButton" name="btn_RESET">
                       <property name="geometry">
                        <rect>
                         <x>340</x>
                         <y>650</y>
                         <width>200</width>
                         <height>50</height>
                        </rect>
                       </property>
                       <property name="font">
                        <font>
                         <family>Times New Roman</family>
                         <pointsize>10</pointsize>
                         <weight>75</weight>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">background-color: rgb(255, 255, 0);</string>
                       </property>
                       <property name="text">
                        <string>RESET</string>
                       </property>
                      </widget>
                      <widget class="QPushButton" name="btn_HOME">
                       <property name="geometry">
                        <rect>
                         <x>340</x>
                         <y>720</y>
                         <width>200</width>
                         <height>50</height>
                        </rect>
                       </property>
                       <property name="font">
                        <font>
                         <family>Times New Roman</family>
                         <pointsize>10</pointsize>
                         <weight>75</weight>
                         <bold>true</bold>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">background-color: rgb(170, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>HOME</string>
                       </property>
                      </widget>
                     </widget>
                    </item>
                   </layout>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="page_1">
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <item>
               <widget class="QFrame" name="frame_9">
                <property name="styleSheet">
                 <string notr="true">background-color: rgb(24, 24, 36);</string>
                </property>
                <property name="frameShape">
                 <enum>QFrame::StyledPanel</enum>
                </property>
                <property name="frameShadow">
                 <enum>QFrame::Raised</enum>
                </property>
                <widget class="QGroupBox" name="groupBox">
                 <property name="geometry">
                  <rect>
                   <x>10</x>
                   <y>70</y>
                   <width>1170</width>
                   <height>331</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>15</pointsize>
                   <weight>75</weight>
                   <italic>true</italic>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(85, 255, 255);
background-color: rgb(24, 24, 36);</string>
                 </property>
                 <property name="title">
                  <string>Setting Delta</string>
                 </property>
                 <widget class="QDoubleSpinBox" name="doubleSpinBox">
                  <property name="geometry">
                   <rect>
                    <x>600</x>
                    <y>230</y>
                    <width>170</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="decimals">
                   <number>2</number>
                  </property>
                  <property name="minimum">
                   <double>-99.000000000000000</double>
                  </property>
                  <property name="singleStep">
                   <double>1.000000000000000</double>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_22">
                  <property name="geometry">
                   <rect>
                    <x>380</x>
                    <y>230</y>
                    <width>190</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>Offset X</string>
                  </property>
                 </widget>
                 <widget class="QDoubleSpinBox" name="doubleSpinBox_2">
                  <property name="geometry">
                   <rect>
                    <x>600</x>
                    <y>270</y>
                    <width>170</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="minimum">
                   <double>-99.000000000000000</double>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_23">
                  <property name="geometry">
                   <rect>
                    <x>380</x>
                    <y>270</y>
                    <width>190</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>Offset Y</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_31">
                  <property name="geometry">
                   <rect>
                    <x>380</x>
                    <y>190</y>
                    <width>220</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>Sản Lượng Hiện Tại </string>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="label_SL">
                  <property name="geometry">
                   <rect>
                    <x>600</x>
                    <y>190</y>
                    <width>170</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_32">
                  <property name="geometry">
                   <rect>
                    <x>380</x>
                    <y>40</y>
                    <width>190</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>Tốc độ Robot </string>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="lTocdoRB">
                  <property name="geometry">
                   <rect>
                    <x>600</x>
                    <y>40</y>
                    <width>170</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0.0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_33">
                  <property name="geometry">
                   <rect>
                    <x>380</x>
                    <y>80</y>
                    <width>190</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>Tốc Độ Bẳng Tải </string>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="lTocdoBT">
                  <property name="geometry">
                   <rect>
                    <x>600</x>
                    <y>80</y>
                    <width>170</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0.0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="label_loaiSP">
                  <property name="geometry">
                   <rect>
                    <x>600</x>
                    <y>120</y>
                    <width>170</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_34">
                  <property name="geometry">
                   <rect>
                    <x>380</x>
                    <y>120</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>Loại Sản Phẩm</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_29">
                  <property name="geometry">
                   <rect>
                    <x>20</x>
                    <y>40</y>
                    <width>100</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="text">
                   <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Theta 1&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_28">
                  <property name="geometry">
                   <rect>
                    <x>20</x>
                    <y>80</y>
                    <width>100</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="text">
                   <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Theta 2&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_27">
                  <property name="geometry">
                   <rect>
                    <x>20</x>
                    <y>120</y>
                    <width>100</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="text">
                   <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Theta 3&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_24">
                  <property name="geometry">
                   <rect>
                    <x>20</x>
                    <y>190</y>
                    <width>100</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="text">
                   <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Axis X&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_25">
                  <property name="geometry">
                   <rect>
                    <x>20</x>
                    <y>230</y>
                    <width>100</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="text">
                   <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Axis Y&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_26">
                  <property name="geometry">
                   <rect>
                    <x>20</x>
                    <y>270</y>
                    <width>100</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="text">
                   <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Axis Z&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="tbThetaX">
                  <property name="geometry">
                   <rect>
                    <x>130</x>
                    <y>40</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="tbThetaY">
                  <property name="geometry">
                   <rect>
                    <x>130</x>
                    <y>80</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="tbThetaZ">
                  <property name="geometry">
                   <rect>
                    <x>130</x>
                    <y>120</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="tbAxisX">
                  <property name="geometry">
                   <rect>
                    <x>130</x>
                    <y>190</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="tbAxisY">
                  <property name="geometry">
                   <rect>
                    <x>130</x>
                    <y>230</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="tbAxisZ">
                  <property name="geometry">
                   <rect>
                    <x>130</x>
                    <y>270</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_30">
                  <property name="geometry">
                   <rect>
                    <x>830</x>
                    <y>80</y>
                    <width>60</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="text">
                   <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Pos_Y&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_35">
                  <property name="geometry">
                   <rect>
                    <x>830</x>
                    <y>40</y>
                    <width>60</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="text">
                   <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Pos_X&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="Pos_X">
                  <property name="geometry">
                   <rect>
                    <x>920</x>
                    <y>40</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="Pos_Y">
                  <property name="geometry">
                   <rect>
                    <x>920</x>
                    <y>80</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_42">
                  <property name="geometry">
                   <rect>
                    <x>830</x>
                    <y>190</y>
                    <width>81</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>QR Loại 2</string>
                  </property>
                 </widget>
                 <widget class="QLabel" name="label_43">
                  <property name="geometry">
                   <rect>
                    <x>830</x>
                    <y>230</y>
                    <width>81</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>QR Loại 3</string>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="label_qr2">
                  <property name="geometry">
                   <rect>
                    <x>920</x>
                    <y>190</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                 <widget class="QLineEdit" name="label_qr3">
                  <property name="geometry">
                   <rect>
                    <x>920</x>
                    <y>230</y>
                    <width>200</width>
                    <height>30</height>
                   </rect>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>13</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="readOnly">
                   <bool>true</bool>
                  </property>
                 </widget>
                </widget>
                <widget class="QPushButton" name="btn_HOME_2">
                 <property name="geometry">
                  <rect>
                   <x>170</x>
                   <y>710</y>
                   <width>160</width>
                   <height>70</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>20</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="autoFillBackground">
                  <bool>false</bool>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background-color: rgb(255, 255, 127);</string>
                 </property>
                 <property name="text">
                  <string>HOME</string>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <property name="autoDefault">
                  <bool>false</bool>
                 </property>
                </widget>
                <widget class="QPushButton" name="btn_RESET_2">
                 <property name="geometry">
                  <rect>
                   <x>350</x>
                   <y>710</y>
                   <width>160</width>
                   <height>70</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>20</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="autoFillBackground">
                  <bool>false</bool>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background-color: rgb(231, 255, 215);</string>
                 </property>
                 <property name="text">
                  <string>RESET</string>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <property name="autoDefault">
                  <bool>false</bool>
                 </property>
                </widget>
                <widget class="QPushButton" name="btn_START_2">
                 <property name="geometry">
                  <rect>
                   <x>530</x>
                   <y>710</y>
                   <width>160</width>
                   <height>70</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>20</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="autoFillBackground">
                  <bool>false</bool>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background-color: rgb(85, 255, 0);</string>
                 </property>
                 <property name="text">
                  <string>START</string>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <property name="autoDefault">
                  <bool>false</bool>
                 </property>
                </widget>
                <widget class="QPushButton" name="btn_STOP_2">
                 <property name="geometry">
                  <rect>
                   <x>710</x>
                   <y>710</y>
                   <width>160</width>
                   <height>70</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>20</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="autoFillBackground">
                  <bool>false</bool>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background-color: rgb(170, 0, 0);</string>
                 </property>
                 <property name="text">
                  <string>STOP</string>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <property name="autoDefault">
                  <bool>false</bool>
                 </property>
                </widget>
                <widget class="QPushButton" name="btn_VACCUM">
                 <property name="geometry">
                  <rect>
                   <x>1020</x>
                   <y>530</y>
                   <width>160</width>
                   <height>70</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>20</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="autoFillBackground">
                  <bool>false</bool>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background-color: rgb(231, 255, 215);</string>
                 </property>
                 <property name="text">
                  <string>Vaccum</string>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <property name="autoDefault">
                  <bool>false</bool>
                 </property>
                </widget>
                <widget class="QPushButton" name="btn_TECHING">
                 <property name="geometry">
                  <rect>
                   <x>1020</x>
                   <y>610</y>
                   <width>160</width>
                   <height>70</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>20</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="autoFillBackground">
                  <bool>false</bool>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background-color: rgb(231, 255, 215);</string>
                 </property>
                 <property name="text">
                  <string>Teching</string>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <property name="autoDefault">
                  <bool>false</bool>
                 </property>
                </widget>
                <widget class="QLabel" name="label_error">
                 <property name="geometry">
                  <rect>
                   <x>10</x>
                   <y>10</y>
                   <width>1210</width>
                   <height>40</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background-color: rgb(24, 24, 36);
color: rgb(255, 0, 0);</string>
                 </property>
                 <property name="text">
                  <string>ERROR</string>
                 </property>
                </widget>
                <widget class="QPushButton" name="btn_RUN">
                 <property name="geometry">
                  <rect>
                   <x>890</x>
                   <y>710</y>
                   <width>160</width>
                   <height>70</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>20</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="autoFillBackground">
                  <bool>false</bool>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background-color: rgb(200, 96, 11);</string>
                 </property>
                 <property name="text">
                  <string>RUN</string>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <property name="autoDefault">
                  <bool>false</bool>
                 </property>
                </widget>
                <widget class="QPushButton" name="btn_man">
                 <property name="geometry">
                  <rect>
                   <x>1020</x>
                   <y>450</y>
                   <width>160</width>
                   <height>70</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>20</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="autoFillBackground">
                  <bool>false</bool>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background-color: rgb(157, 157, 157);</string>
                 </property>
                 <property name="text">
                  <string>MAN</string>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <property name="autoDefault">
                  <bool>false</bool>
                 </property>
                </widget>
                <widget class="QLabel" name="label_39">
                 <property name="geometry">
                  <rect>
                   <x>250</x>
                   <y>550</y>
                   <width>300</width>
                   <height>30</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>13</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(0, 255, 255);</string>
                 </property>
                 <property name="frameShape">
                  <enum>QFrame::NoFrame</enum>
                 </property>
                 <property name="text">
                  <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;nhập từ (-120 ==&amp;gt; 150)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                 </property>
                </widget>
                <widget class="QLabel" name="label_38">
                 <property name="geometry">
                  <rect>
                   <x>250</x>
                   <y>470</y>
                   <width>300</width>
                   <height>30</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>13</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(0, 255, 255);</string>
                 </property>
                 <property name="frameShape">
                  <enum>QFrame::NoFrame</enum>
                 </property>
                 <property name="text">
                  <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;nhập từ (-120 ==&amp;gt; 120)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                 </property>
                </widget>
                <widget class="QLineEdit" name="lineEdit_TD_X">
                 <property name="geometry">
                  <rect>
                   <x>250</x>
                   <y>430</y>
                   <width>200</width>
                   <height>40</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(0, 255, 255);</string>
                 </property>
                </widget>
                <widget class="QLabel" name="label_36">
                 <property name="geometry">
                  <rect>
                   <x>40</x>
                   <y>440</y>
                   <width>200</width>
                   <height>30</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>13</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(0, 255, 255);</string>
                 </property>
                 <property name="frameShape">
                  <enum>QFrame::NoFrame</enum>
                 </property>
                 <property name="text">
                  <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Nhập Tọa Độ X&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                 </property>
                </widget>
                <widget class="QLineEdit" name="lineEdit_TD_Y">
                 <property name="geometry">
                  <rect>
                   <x>250</x>
                   <y>510</y>
                   <width>200</width>
                   <height>40</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(0, 255, 255);</string>
                 </property>
                </widget>
                <widget class="QLabel" name="label_37">
                 <property name="geometry">
                  <rect>
                   <x>40</x>
                   <y>520</y>
                   <width>200</width>
                   <height>30</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>13</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(0, 255, 255);</string>
                 </property>
                 <property name="frameShape">
                  <enum>QFrame::NoFrame</enum>
                 </property>
                 <property name="text">
                  <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Nhập Tọa Độ Y&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                 </property>
                </widget>
                <widget class="QLineEdit" name="lineEdit_TD_Z">
                 <property name="geometry">
                  <rect>
                   <x>250</x>
                   <y>600</y>
                   <width>200</width>
                   <height>40</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(0, 255, 255);</string>
                 </property>
                </widget>
                <widget class="QLabel" name="label_40">
                 <property name="geometry">
                  <rect>
                   <x>250</x>
                   <y>640</y>
                   <width>300</width>
                   <height>30</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>13</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(0, 255, 255);</string>
                 </property>
                 <property name="frameShape">
                  <enum>QFrame::NoFrame</enum>
                 </property>
                 <property name="text">
                  <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;nhập từ (-350 ==&amp;gt; -450)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                 </property>
                </widget>
                <widget class="QLabel" name="label_41">
                 <property name="geometry">
                  <rect>
                   <x>40</x>
                   <y>610</y>
                   <width>200</width>
                   <height>30</height>
                  </rect>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>13</pointsize>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(0, 255, 255);</string>
                 </property>
                 <property name="frameShape">
                  <enum>QFrame::NoFrame</enum>
                 </property>
                 <property name="text">
                  <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;Nhập Tọa Độ Z&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
                 </property>
                </widget>
               </widget>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="page_2">
             <layout class="QHBoxLayout" name="horizontalLayout_12">
              <item>
               <widget class="QFrame" name="frame_6">
                <property name="styleSheet">
                 <string notr="true">background-color: rgb(24, 24, 36);</string>
                </property>
                <property name="frameShape">
                 <enum>QFrame::StyledPanel</enum>
                </property>
                <property name="frameShadow">
                 <enum>QFrame::Raised</enum>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="page_5">
             <layout class="QHBoxLayout" name="horizontalLayout_11">
              <item>
               <widget class="QFrame" name="frame_16">
                <property name="styleSheet">
                 <string notr="true">background-color: rgb(24, 24, 36);</string>
                </property>
                <property name="frameShape">
                 <enum>QFrame::StyledPanel</enum>
                </property>
                <property name="frameShadow">
                 <enum>QFrame::Raised</enum>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item alignment="Qt::AlignBottom">
        <widget class="QFrame" name="footer">
         <property name="font">
          <font>
           <family>Times New Roman</family>
           <pointsize>14</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="frame_10">
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="lb_model">
               <property name="font">
                <font>
                 <family>Times New Roman</family>
                 <pointsize>13</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(170, 255, 255);
</string>
               </property>
               <property name="text">
                <string>Nguyễn Vỉ Khang</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="frame_11">
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item alignment="Qt::AlignRight">
              <widget class="QPushButton" name="system_active">
               <property name="font">
                <font>
                 <family>Times New Roman</family>
                 <pointsize>15</pointsize>
                 <weight>75</weight>
                 <italic>false</italic>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(0, 255, 255);</string>
               </property>
               <property name="text">
                <string>System</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="size_grip">
            <property name="minimumSize">
             <size>
              <width>10</width>
              <height>10</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>10</width>
              <height>10</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="icon.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>btn_menu</sender>
   <signal>toggled(bool)</signal>
   <receiver>slide_menu_container</receiver>
   <slot>setVisible(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>192</x>
     <y>32</y>
    </hint>
    <hint type="destinationlabel">
     <x>23</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btn_exit</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>close()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>105</x>
     <y>880</y>
    </hint>
    <hint type="destinationlabel">
     <x>219</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
