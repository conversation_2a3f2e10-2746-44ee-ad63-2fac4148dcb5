import cv2
import numpy as np
from pyzbar.pyzbar import decode

# <PERSON><PERSON><PERSON>nh gốc
image_path = r"C:\Users\<USER>\OneDrive\Desktop\barcode_020524\assets\99.png"
image = cv2.imread(image_path)

# <PERSON><PERSON><PERSON><PERSON> sang grayscale
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# Áp dụng bộ lọc để cải thiện độ tương phản
gray = cv2.equalizeHist(gray)

# Làm sắc nét ảnh
kernel = np.array([[0, -1, 0], [-1, 5, -1], [0, -1, 0]])
sharpened = cv2.filter2D(gray, -1, kernel)

# <PERSON>hận diện mã vạch
decoded_objects = decode(sharpened)

# Hi<PERSON><PERSON> thị kết quả
for obj in decoded_objects:
    (x, y, w, h) = obj.rect
    cv2.rectangle(image, (x, y), (x + w, y + h), (0, 255, 0), 2)

    barcode_data = obj.data.decode("utf-8")
    barcode_type = obj.type
    text = f"{barcode_type}: {barcode_data}"
    cv2.putText(image, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

    print(f"Loại: {barcode_type}, Nội dung: {barcode_data}")

cv2.imwrite("barcode_detected.png", gray)
cv2.imwrite("barcode_detected.png", sharpened)
cv2.imshow("Processed Barcode Detection", image)
cv2.waitKey(0)
cv2.destroyAllWindows()
