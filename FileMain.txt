from PyQt5.QtWidgets import *
from PyQt5.QtGui import  *
from PyQt5.QtCore import *
import pypylon.pylon as pylon
from pyzbar import pyzbar
import sys
import cv2
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
import icon_rc
from UI_form import Ui_MainWindow
from CamOperation_class import CameraOperation
import ctypes

def TxtWrapBy(start_str, end_str, all_str):
    # Tìm vị trí bắt đầu của start_str
    start = all_str.find(start_str)
    if start == -1:
        return None  # Không tìm thấy start_str, trả về None hoặc giá trị khác tùy ý
    
    # Di chuyển con trỏ start đến sau start_str
    start += len(start_str)
    
    # Tìm vị trí kết thúc của end_str
    end = all_str.find(end_str, start)
    if end == -1:
        return None  # Không tìm thấy end_str, tr<PERSON> về None hoặc giá trị khác
    
    # Trả về chuỗi con trong khoảng từ start đến end
    return all_str[start:end].strip()

# # Ví dụ sử dụng
# text = "Hello, this is a test. Let's extract this part from here."
# result = TxtWrapBy("this", "from", text)
# print(result)  # Output: "is a test."



def ToHexStr(num):
    # Dictionary chuyển đổi các giá trị 10-15 thành 'a'-'f'
    chaDic = {10: 'a', 11: 'b', 12: 'c', 13: 'd', 14: 'e', 15: 'f'}
    
    # Nếu số bằng 0, trả về "0" ngay lập tức
    if num == 0:
        return "0"
    
    # Nếu số âm, chuyển sang giá trị không dấu 32 bit
    if num < 0:
        num = num + 2 ** 32
    
    # Biến để chứa kết quả hex
    hexStr = ""
    
    # Lặp qua và chuyển mỗi phần dư (mod 16) thành ký tự hex
    while num > 0:
        digit = num % 16
        hexStr = chaDic.get(digit, str(digit)) + hexStr
        num //= 16
    
    return hexStr

# # Ví dụ sử dụng
# print(ToHexStr(255))    # Output: "ff"
# print(ToHexStr(-1))     # Output: "ffffffff"
# print(ToHexStr(0))      # Output: "0"
# print(ToHexStr(123456)) # Output: "1e240"


if __name__ == "__main__":
    global deviceList
    deviceList = MV_CC_DEVICE_INFO_LIST()
    global cam
    cam = MvCamera()
    global nSelCamIndex
    nSelCamIndex = 0
    global obj_cam_operation
    obj_cam_operation = 0
    global isOpen
    isOpen = False
    global isGrabbing
    isGrabbing = False
    global isCalibMode  # Cho dù đó là chế độ hiệu chỉnh (lấy ảnh gốc)
    isCalibMode = True



    # Liên kết danh sách thả xuống với chỉ mục thông tin thiết bị
    def xFunc(event):
        global nSelCamIndex
        nSelCamIndex = TxtWrapBy("[", "]", ui.ComboDevices.get())

    # Giải mã ký tự
    def decoding_char(c_ubyte_value):
        c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
        try:
            decode_str = c_char_p_value.value.decode('gbk')  # Chinese characters
        except UnicodeDecodeError:
            decode_str = str(c_char_p_value.value)
        return decode_str

    
    def enum_devices():
        global deviceList
        global obj_cam_operation

        deviceList = MV_CC_DEVICE_INFO_LIST()
        ret = MvCamera.MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, deviceList)
        if ret != 0:
            strError = "Enum devices fail! ret = :" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(mainWindow, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print("Find %d devices!" % deviceList.nDeviceNum)

        devList = []
        for i in range(0, deviceList.nDeviceNum):
            mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
            if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE:
                print("\ngige device: [%d]" % i)
                user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                model_name = decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                print("device user define name: " + user_defined_name)
                print("device model name: " + model_name)

                nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))
                ui.lb_ipcamera.setText(str(nip1) + "." + str(nip2) + "." + str(nip3) + "." + str(nip4))
                devList.append(
                    "[" + str(i) + "]GigE: " + user_defined_name + " " + model_name + "(" + str(nip1) + "." + str(
                        nip2) + "." + str(nip3) + "." + str(nip4) + ")")
            elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                print("\nu3v device: [%d]" % i)
                user_defined_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                model_name = decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                print("device user define name: " + user_defined_name)
                print("device model name: " + model_name)

                strSerialNumber = ""
                for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                    if per == 0:
                        break
                    strSerialNumber = strSerialNumber + chr(per)
                print("user serial number: " + strSerialNumber)
                devList.append("[" + str(i) + "]USB: " + user_defined_name + " " + model_name
                               + "(" + str(strSerialNumber) + ")")

        ui.ComboDevices.clear()
        ui.ComboDevices.addItems(devList)
        ui.ComboDevices.setCurrentIndex(0)

    # copen device
    def open_device():
        global deviceList
        global nSelCamIndex
        global obj_cam_operation
        global isOpen
        if isOpen:
            QMessageBox.warning(mainWindow, "Error", 'Camera is Running!', QMessageBox.Ok)
            return MV_E_CALLORDER

        nSelCamIndex = ui.ComboDevices.currentIndex()
        if nSelCamIndex < 0:
            QMessageBox.warning(mainWindow, "Error", 'Please select a camera!', QMessageBox.Ok)
            return MV_E_CALLORDER

        obj_cam_operation = CameraOperation(cam, deviceList, nSelCamIndex)
        ret = obj_cam_operation.Open_device()
        if 0 != ret:
            strError = "Open device failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
            isOpen = False
        else:
            set_continue_mode()
            get_param()
            isOpen = True
            ui.label_text.setText("Open device successfully!")
            enable_controls()

    # Start grab image
    def start_grabbing():
        global obj_cam_operation
        global isGrabbing

        ret = obj_cam_operation.Start_grabbing(ui.widgetDisplay)

        if ret != 0:
            strError = "Start grabbing failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            isGrabbing = True
            ui.label_text.setText("Start grabbing successfully!")
            enable_controls()
    
    def update_data(self, data):
        ui.tableWidget_out.clear()
        for row, obj in enumerate(data):
            ui.tableWidget_out.insertRow(row)
            ui.tableWidget_out.setItem(row, 0, QTableWidgetItem(str(obj)))


    # Stop grab image
    def stop_grabbing():
        global obj_cam_operation
        global isGrabbing
        ret = obj_cam_operation.Stop_grabbing()
        if ret != 0:
            strError = "Stop grabbing failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            isGrabbing = False
            ui.label_text.setText("Stop grabbing successfully!")
            enable_controls()

    # Close device
    def close_device():
        global isOpen
        global isGrabbing
        global obj_cam_operation

        if isOpen:
            obj_cam_operation.Close_device()
            isOpen = False

        isGrabbing = False

        enable_controls()
    


    # set trigger mode
    def set_continue_mode():
        strError = None
        trigger_mode_enabled = False  # Define the variable here

        ret = obj_cam_operation.Set_trigger_mode(False)
        if ret != 0:
            strError = "Set continue mode failed ret:" + ToHexStr(ret) + " mode is " + str(trigger_mode_enabled)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            ui.radioContinueMode.setChecked(True)
            ui.radioTriggerMode.setChecked(False)
            ui.bnSoftwareTrigger.setEnabled(False)

    # set software trigger mode
    def set_software_trigger_mode():

        ret = obj_cam_operation.Set_trigger_mode(True)
        if ret != 0:
            strError = "Set trigger mode failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            ui.radioContinueMode.setChecked(False)
            ui.radioTriggerMode.setChecked(True)
            ui.bnSoftwareTrigger.setEnabled(isGrabbing)

    # set trigger software
    def trigger_once():
        ret = obj_cam_operation.Trigger_once()
        if ret != 0:
            strError = "TriggerSoftware failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)

    # save image
    def save_bmp():
        ret = obj_cam_operation.Save_Bmp()
        if ret != MV_OK:
            strError = "Save BMP failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            print("Save image success")
            
    def is_float(str):
        try:
            float(str)
            return True
        except ValueError:
            return False
    

    # get param
    def get_param():
        ret = obj_cam_operation.Get_parameter()
        if ret != MV_OK:
            strError = "Get param failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
        else:
            ui.edtExposureTime.setText("{0:.2f}".format(obj_cam_operation.exposure_time))
            ui.edtGain.setText("{0:.2f}".format(obj_cam_operation.gain))
            ui.edtFrameRate.setText("{0:.2f}".format(obj_cam_operation.frame_rate))

    # set param
    def set_param():
        frame_rate = ui.edtFrameRate.text()
        exposure = ui.edtExposureTime.text()
        gain = ui.edtGain.text()

        if is_float(frame_rate)!=True or is_float(exposure)!=True or is_float(gain)!=True:
            strError = "Set param failed ret:" + ToHexStr(MV_E_PARAMETER)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)
            return MV_E_PARAMETER
        
        ret = obj_cam_operation.Set_parameter(frame_rate, exposure, gain)
        if ret != MV_OK:
            strError = "Set param failed ret:" + ToHexStr(ret)
            QMessageBox.warning(mainWindow, "Error", strError, QMessageBox.Ok)

        return MV_OK

    # set enable status
    def enable_controls():
        global isGrabbing
        global isOpen

        ui.bnOpen.setEnabled(not isOpen)
        ui.bnClose.setEnabled(isOpen)
        ui.btn_ConnectCamera.setEnabled(isOpen and (not isGrabbing))
        ui.bnStop.setEnabled(isOpen and isGrabbing)
        ui.bnSoftwareTrigger.setEnabled(isGrabbing and ui.radioTriggerMode.isChecked())
        ui.bnSaveImage.setEnabled(isOpen and isGrabbing)
    

    @pyqtSlot(bool)
    def on_btn_menu_clicked(checked):
        if checked:
            ui.slide_menu_container.show()
        else:
            ui.slide_menu_container.hide()
    @pyqtSlot(bool)
    def on_btn_Home_clicked(checked):
            ui.stackedWidget.setCurrentIndex(0)
    @pyqtSlot(bool)
    def on_btn_Manual_clicked(checked):
            ui.stackedWidget.setCurrentIndex(1)
    @pyqtSlot(bool)
    def on_btn_Setting_clicked(checked):
            ui.stackedWidget.setCurrentIndex(2)
    @pyqtSlot(bool)
    def on_btn_User_clicked(checked):
            ui.stackedWidget.setCurrentIndex(3)
    @pyqtSlot(bool)
    def on_btn_exit_clicked(checked):
        close_device()
        sys.exit()
        print("Exit Program")
        

    #Init app, bind ui and api
    app = QApplication(sys.argv)
    mainWindow = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(mainWindow)
    ui.slide_menu_container.hide()
    ui.stackedWidget.setCurrentIndex(0)
    ui.btn_menu.setChecked(True)
    ui.btn_Home.clicked.connect(on_btn_Home_clicked)
    ui.btn_Manual.clicked.connect(on_btn_Manual_clicked)
    ui.btn_Setting.clicked.connect(on_btn_Setting_clicked)
    ui.btn_User.clicked.connect(on_btn_User_clicked)

    ui.btn_exit.clicked.connect(on_btn_exit_clicked)
    ui.btn_menu.clicked.connect(on_btn_menu_clicked)
    ui.btn_Scan.clicked.connect(enum_devices)
    ui.bnOpen.clicked.connect(open_device)
    ui.bnClose.clicked.connect(close_device)
    ui.btn_ConnectCamera.clicked.connect(start_grabbing)
    ui.bnStop.clicked.connect(stop_grabbing)

    ui.bnSoftwareTrigger.clicked.connect(trigger_once)
    ui.radioTriggerMode.clicked.connect(set_software_trigger_mode)
    ui.radioContinueMode.clicked.connect(set_continue_mode)

    ui.bnGetParam.clicked.connect(get_param)
    ui.bnSetParam.clicked.connect(set_param)
    ui.bnSaveImage.clicked.connect(save_bmp)
    

    mainWindow.show()
    app.exec_()
    close_device()
    sys.exit()