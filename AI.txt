import cv2
from ultralytics import YOLO

# Convert YOLO model to OpenVino format for deloy on CPU with x3 speedup
def openVino(path, model):
    model = YOLO(model)
    model.export(format="openvino")
    ov_model = YOLO('model/best_openvino_model/')
    result = ov_model.predict(path, conf=0.5, show=True, show_conf=False)

# Process the result of the YOLO model
def rusultProcessing(path, model):
    clss = []
    boxes = []
    conf = []
    model = YOLO(model)
    result = model.predict(path, conf=0.5, show=True, show_conf=False)
    clss = result[0].boxes.cls.tolist()
    boxes = result[0].boxes.xyxy.tolist()
    conf = result[0].boxes.conf.tolist()
    return clss, boxes, conf

if __name__ == "__main__":
    path_vid = 'images/source.mp4'
    path = 'images/vest.jpg'
    img = cv2.imread(path)
    print(rusultProcessing(path, 'model/best.pt'))

