from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QMessageBox, QPushButton, QHBoxLayout, QVBoxLayout, QLabel, QStackedWidget
from PyQt5.QtGui import QIcon, QPixmap, QPixmap, QImage
from PyQt5.QtCore import Qt, pyqtSlot, QFile, QTextStream, QTimer, QPropertyAnimation, QRect, QDateTime
import pypylon.pylon as pylon
import sys
import cv2
import os

import icon_rc
from ui_form import Ui_MainWindow

class MainWindow(QMainWindow):
    def __init__(self):
        super(MainWindow, self).__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        self.ui.slide_menu_container.hide()
        self.ui.stackedWidget.setCurrentIndex(0)
        self.ui.btn_menu.setChecked(True)
        self.ConnectCamera = False

        self.ui.btn_Home.clicked.connect(self.on_btn_Home_clicked)
        self.ui.btn_Manual.clicked.connect(self.on_btn_Manual_clicked)
        self.ui.btn_Setting.clicked.connect(self.on_btn_Setting_clicked)
        self.ui.btn_User.clicked.connect(self.on_btn_User_clicked)
        self.ui.btn_exit.clicked.connect(self.on_btn_exit_clicked)
        self.ui.btn_menu.clicked.connect(self.on_btn_menu_clicked)

        self.ui.btn_ConnectCamera.clicked.connect(self.open_camera)
        # self.ui.btn_ConnectPLC.clicked.connect(self.open_camera)
        self.ui.btn_Start_Program.clicked.connect(self.set_CheckConnect)
        # self.ui.btn_Stop_Program.clicked.connect(self.open_camera)

    def set_CheckConnect(self):
        if self.ConnectCamera:
            print("Is connect Camera")
        else:
            print("Is not connect Camera")
            QMessageBox.critical(self, "Error","Is not connect Camera")

## function for open camera
    def open_camera(self):
        self.cap = cv2.VideoCapture(0)

        if not self.cap.isOpened():
            QMessageBox.warning(self, "Error", "Cannot open camera")
            print("Is not open Camera")
            return
        else:
            self.ConnectCamera = True
            self.timer = QTimer(self)
            self.timer.timeout.connect(self.update_camera)
            self.timer.start(30)
            print("Connected Camera")


## Functions for buttons menu
    @pyqtSlot(bool)
    def on_btn_menu_clicked(self, checked):
        if checked:
            self.ui.slide_menu_container.show()
        else:
            self.ui.slide_menu_container.hide()

## Functions for buttons Home
    @pyqtSlot(bool)
    def on_btn_Home_clicked(self, checked):
        self.ui.stackedWidget.setCurrentIndex(0)

## Functions for buttons Manual
    @pyqtSlot(bool)
    def on_btn_Manual_clicked(self, checked):
        self.ui.stackedWidget.setCurrentIndex(1)

## Functions for buttons Setting
    @pyqtSlot(bool)
    def on_btn_Setting_clicked(self, checked):
        self.ui.stackedWidget.setCurrentIndex(2)

## Functions for buttons User
    @pyqtSlot(bool)
    def on_btn_User_clicked(self, checked):
        self.ui.stackedWidget.setCurrentIndex(3)

## Functions for buttons Exit
    @pyqtSlot(bool)
    def on_btn_exit_clicked(self, checked):
        self.close()
        print("Exit Program")

## Functions for stream camera
    def update_camera(self):
        ret, frame = self.cap.read()
        if ret:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            frame = cv2.flip(frame, 1)
            frame = cv2.resize(frame, (640, 480))
            qt_image = QImage(frame, frame.shape[1], frame.shape[0], QImage.Format_RGB888)
            self.ui.label_stream_camera.setPixmap(QPixmap.fromImage(qt_image))

    def closeEvent(self, event):
        self.cap.release()
        event.accept()
        print("Exit Program")
        



if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())