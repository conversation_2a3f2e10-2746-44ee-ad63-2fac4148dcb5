import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# Thông số robot Delta
e = 40
f = 100
re = 350
rf = 150

sqrt3 = np.sqrt(3.0)
pi = np.pi
sin120 = sqrt3 / 2.0
cos120 = -0.5
tan60 = sqrt3
sin30 = 0.5
tan30 = 1 / sqrt3

# Hàm hỗ trợ tính toán động học nghịch
def delta_calcAngleYZ(x0, y0, z0):
    y1 = -0.5 * 0.57735 * f
    y0 -= 0.5 * 0.57735 * e
    a = (x0**2 + y0**2 + z0**2 + rf**2 - re**2 - y1**2) / (2 * z0)
    b = (y1 - y0) / z0
    d = -(a + b * y1)**2 + rf * (b**2 * rf + rf)
    
    if d < 0:
        return None
    
    yj = (y1 - a * b - np.sqrt(d)) / (b**2 + 1)
    zj = a + b * yj
    theta = np.degrees(np.arctan(-zj / (y1 - yj)))
    if yj > y1:
        theta += 180.0
    
    return theta

# Động học nghịch: (x0, y0, z0) → (theta1, theta2, theta3)
def delta_calcInverse(x0, y0, z0):
    theta1 = delta_calcAngleYZ(x0, y0, z0)
    theta2 = delta_calcAngleYZ(x0 * cos120 + y0 * sin120, y0 * cos120 - x0 * sin120, z0)
    theta3 = delta_calcAngleYZ(x0 * cos120 - y0 * sin120, y0 * cos120 + x0 * sin120, z0)
    
    if None in [theta1, theta2, theta3]:
        return None
    
    return theta1, theta2, theta3

# Vẽ mô phỏng Robot Delta
def plot_delta_robot_inverse(x0, y0, z0):
    result = delta_calcInverse(x0, y0, z0)
    if result is None:
        print("Không tìm thấy vị trí hợp lệ!")
        return
    
    theta1, theta2, theta3 = result
    print(f"Góc động cơ cho vị trí ({x0}, {y0}, {z0}):")
    print(f"  θ1 = {theta1:.2f}°")
    print(f"  θ2 = {theta2:.2f}°")
    print(f"  θ3 = {theta3:.2f}°\n")

    fig = plt.figure(figsize=(10, 10))
    ax = fig.add_subplot(111, projection='3d')

    # Xác định tọa độ của base và end effector
    base_points = np.array([[50, -50, 0], [-50, -50, 0], [0, 100, 0], [50, -50, 0]])
    end_effector_points = np.array([
        [x0 + 20, y0 - 20, z0], 
        [x0 - 20, y0 - 20, z0], 
        [x0, y0 + 40, z0],
        [x0 + 20, y0 - 20, z0]
    ])

    # Vẽ base
    ax.plot(*base_points.T, 'ko-', label='Base')

    # Vẽ end effector
    ax.plot(*end_effector_points.T, 'ro-', label='End Effector')

    # Vẽ liên kết giữa base và end effector
    for i in range(3):
        ax.plot([base_points[i, 0], end_effector_points[i, 0]], 
                [base_points[i, 1], end_effector_points[i, 1]], 
                [base_points[i, 2], end_effector_points[i, 2]], 
                'b-', linewidth=2)

    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_xlim([-150, 150])
    ax.set_ylim([-150, 150])
    ax.set_zlim([-600, 100])
    ax.set_title(f"Delta Robot Kinematics (IK)\nEnd Effector: ({x0}, {y0}, {z0})\n θ1={theta1:.2f}°, θ2={theta2:.2f}°, θ3={theta3:.2f}°")
    
    plt.legend()
    plt.show()

# Nhập tọa độ mong muốn
x0, y0, z0 = 51.7461, 106.517, -427.094  # Tọa độ đầu cuối
plot_delta_robot_inverse(x0, y0, z0)
