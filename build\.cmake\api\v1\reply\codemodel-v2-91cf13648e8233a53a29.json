{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Yolov8CPPInference", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-0d6b8c3551a897f1c4b8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "Yolov8CPPInference::@6890427a1f51a3e7e1df", "jsonFile": "target-Yolov8CPPInference-Debug-b931a7b8ef84be1530e2.json", "name": "Yolov8CPPInference", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-11f3bc3ee7f584acec6e.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "Yolov8CPPInference", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-0d6b8c3551a897f1c4b8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "Yolov8CPPInference::@6890427a1f51a3e7e1df", "jsonFile": "target-Yolov8CPPInference-Release-5d52575c4d17c15f1cb8.json", "name": "Yolov8CPPInference", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-11f3bc3ee7f584acec6e.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "Yolov8CPPInference", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-0d6b8c3551a897f1c4b8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "Yolov8CPPInference::@6890427a1f51a3e7e1df", "jsonFile": "target-Yolov8CPPInference-MinSizeRel-14421922edd07ecd2863.json", "name": "Yolov8CPPInference", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-11f3bc3ee7f584acec6e.json", "name": "ZERO_CHECK", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Yolov8CPPInference", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-0d6b8c3551a897f1c4b8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "Yolov8CPPInference::@6890427a1f51a3e7e1df", "jsonFile": "target-Yolov8CPPInference-RelWithDebInfo-8a2622ca49926b6a3b3c.json", "name": "Yolov8CPPInference", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-11f3bc3ee7f584acec6e.json", "name": "ZERO_CHECK", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/DO_AN_TN/MainForm/build", "source": "C:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference"}, "version": {"major": 2, "minor": 7}}