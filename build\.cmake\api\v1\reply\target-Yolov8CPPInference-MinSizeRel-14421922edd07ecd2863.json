{"artifacts": [{"path": "MinSizeRel/Yolov8CPPInference.exe"}, {"path": "MinSizeRel/Yolov8CPPInference.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 32, "parent": 0}, {"command": 1, "file": 0, "line": 33, "parent": 0}, {"command": 2, "file": 0, "line": 21, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG"}], "includes": [{"path": "C:/DO_AN_TN/MainForm/build"}, {"path": "C:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference"}, {"backtrace": 3, "path": "C:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference/getopt"}, {"backtrace": 4, "isSystem": true, "path": "C:/opencv/build/include"}], "language": "CXX", "sourceIndexes": [0, 2]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /MD /O1 /Ob1 /DNDEBUG"}], "includes": [{"path": "C:/DO_AN_TN/MainForm/build"}, {"path": "C:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference"}, {"backtrace": 3, "path": "C:/DO_AN_TN/MainForm/ultralytics/examples/YOLOv8-CPP-Inference/getopt"}, {"backtrace": 4, "isSystem": true, "path": "C:/opencv/build/include"}], "language": "C", "sourceIndexes": [4]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "Yolov8CPPInference::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4100.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Yolov8CPPInference", "nameOnDisk": "Yolov8CPPInference.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "inference.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "inference.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "getopt/getopt.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "getopt/getopt.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}