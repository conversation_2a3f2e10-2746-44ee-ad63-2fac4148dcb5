
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.2+c078802d4 for .NET Framework
      Build started 2/5/2025 1:07:11 PM.
      
      Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx86\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.19
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.2+c078802d4 for .NET Framework
      Build started 2/5/2025 1:07:12 PM.
      
      Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx86\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.66
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-lm61k3"
      binary: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-lm61k3"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-lm61k3'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_203c5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.2+c078802d4 for .NET Framework
        Build started 2/5/2025 1:07:13 PM.
        
        Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lm61k3\\cmTC_203c5.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_203c5.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lm61k3\\Debug\\".
          Creating directory "cmTC_203c5.dir\\Debug\\cmTC_203c5.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_203c5.dir\\Debug\\cmTC_203c5.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_203c5.dir\\Debug\\cmTC_203c5.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_203c5.dir\\Debug\\\\" /Fd"cmTC_203c5.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_203c5.dir\\Debug\\\\" /Fd"cmTC_203c5.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lm61k3\\Debug\\cmTC_203c5.exe" /INCREMENTAL /ILK:"cmTC_203c5.dir\\Debug\\cmTC_203c5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-lm61k3/Debug/cmTC_203c5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-lm61k3/Debug/cmTC_203c5.lib" /MACHINE:X64  /machine:x64 cmTC_203c5.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_203c5.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lm61k3\\Debug\\cmTC_203c5.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_203c5.dir\\Debug\\cmTC_203c5.tlog\\unsuccessfulbuild".
          Touching "cmTC_203c5.dir\\Debug\\cmTC_203c5.tlog\\cmTC_203c5.lastbuildstate".
        Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lm61k3\\cmTC_203c5.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.89
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-u5o4h9"
      binary: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-u5o4h9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-u5o4h9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e7c82.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.2+c078802d4 for .NET Framework
        Build started 2/5/2025 1:07:15 PM.
        
        Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u5o4h9\\cmTC_e7c82.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e7c82.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u5o4h9\\Debug\\".
          Creating directory "cmTC_e7c82.dir\\Debug\\cmTC_e7c82.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e7c82.dir\\Debug\\cmTC_e7c82.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e7c82.dir\\Debug\\cmTC_e7c82.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_e7c82.dir\\Debug\\\\" /Fd"cmTC_e7c82.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_e7c82.dir\\Debug\\\\" /Fd"cmTC_e7c82.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u5o4h9\\Debug\\cmTC_e7c82.exe" /INCREMENTAL /ILK:"cmTC_e7c82.dir\\Debug\\cmTC_e7c82.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-u5o4h9/Debug/cmTC_e7c82.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-u5o4h9/Debug/cmTC_e7c82.lib" /MACHINE:X64  /machine:x64 cmTC_e7c82.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_e7c82.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u5o4h9\\Debug\\cmTC_e7c82.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e7c82.dir\\Debug\\cmTC_e7c82.tlog\\unsuccessfulbuild".
          Touching "cmTC_e7c82.dir\\Debug\\cmTC_e7c82.tlog\\cmTC_e7c82.lastbuildstate".
        Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u5o4h9\\cmTC_e7c82.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDAToolkit.cmake:681 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDAToolkit.cmake:802 (_CUDAToolkit_find_root_dir)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      Executed nvcc to extract CUDAToolkit information:
      
      c:\\DO_AN_TN\\MainForm\\build>call "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x64/../../../../../../../VC/Auxiliary/Build/vcvars64.bat" 
      **********************************************************************
      ** Visual Studio 2022 Developer Command Prompt v17.11.0
      ** Copyright (c) 2022 Microsoft Corporation
      **********************************************************************
      [vcvarsall.bat] Environment initialized for: 'x64'
      nvcc warning : Support for offline compilation for architectures prior to '<compute/sm/lto>_75' will be removed in a future release (Use -Wno-deprecated-gpu-targets to suppress warning).
      #$ C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.41.34120/bin/HostX86/x64/../../../../../../../VC/Auxiliary/Build/vcvars64.bat
      #$ ALLUSERSPROFILE=C:\\ProgramData
      #$ APPDATA=C:\\Users\\<USER>\\AppData\\Roaming
      #$ AutInstLog=C:\\ProgramData\\Siemens\\Automation\\Logfiles\\Setup\\
      #$ CC=cl.exe
      #$ CHROME_CRASHPAD_PIPE_NAME=\\\\.\\pipe\\crashpad_15816_AXULKPWLUDWYDGEM
      #$ CMAKE_PREFIX_PATH=C:\\opencv\\build
      #$ CommandPromptType=Native
      #$ CommonProgramFiles=C:\\Program Files\\Common Files
      #$ CommonProgramFiles(x86)=C:\\Program Files (x86)\\Common Files
      #$ CommonProgramW6432=C:\\Program Files\\Common Files
      #$ COMPUTERNAME=KHANG
      #$ ComSpec=C:\\WINDOWS\\system32\\cmd.exe
      #$ CUDA_PATH=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8
      #$ CUDA_PATH_V12_8=C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8
      #$ CXX=cl.exe
      #$ DevEnvDir=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\
      #$ DriverData=C:\\Windows\\System32\\Drivers\\DriverData
      #$ EFC_15652=1
      #$ ELECTRON_RUN_AS_NODE=1
      #$ ExtensionSdkDir=C:\\Program Files (x86)\\Microsoft SDKs\\Windows Kits\\10\\ExtensionSDKs
      #$ EXTERNAL_INCLUDE=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\include;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\include;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include;C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\um;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\shared;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\winrt;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\cppwinrt;C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um
      #$ FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
      #$ FPS_BROWSER_USER_PROFILE_STRING=Default
      #$ Framework40Version=v4.0
      #$ FrameworkDir=C:\\Windows\\Microsoft.NET\\Framework64\\
      #$ FrameworkDIR32=C:\\Windows\\Microsoft.NET\\Framework\\
      #$ FrameworkDir64=C:\\Windows\\Microsoft.NET\\Framework64\\
      #$ FrameworkVersion=v4.0.30319
      #$ FrameworkVersion32=v4.0.30319
      #$ FrameworkVersion64=v4.0.30319
      #$ FSHARPINSTALLDIR=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\FSharp\\Tools
      #$ GENICAM_GENTL32_PATH=C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win32_i86;C:\\Program Files\\Basler\\pylon 8\\Runtime\\Win32\\;C:\\Program Files\\MySQL\\MySQL Server 9.1\\bin
      #$ GENICAM_GENTL64_PATH=C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win64_x64;C:\\Program Files\\Basler\\pylon 8\\Runtime\\x64\\
      #$ HOMEDRIVE=C:
      #$ HOMEPATH=\\Users\\khang
      #$ INCLUDE=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\include;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\include;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include;C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\um;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\shared;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\winrt;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\cppwinrt;C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\include;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\include;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include;C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\um;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\shared;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\winrt;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\cppwinrt;C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um
      #$ IntelliJ IDEA=C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.2.1\\bin;
      #$ IntelliJ IDEA Community Edition=C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.2.1\\bin;
      #$ LIB=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\lib\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\lib\\x64;C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\lib\\um\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\lib\\10.0.22621.0\\ucrt\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\\\lib\\10.0.22621.0\\\\um\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\lib\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\lib\\x64;C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\lib\\um\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\lib\\10.0.22621.0\\ucrt\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\\\lib\\10.0.22621.0\\\\um\\x64
      #$ LIBPATH=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\lib\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\lib\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\lib\\x86\\store\\references;C:\\Program Files (x86)\\Windows Kits\\10\\UnionMetadata\\10.0.22621.0;C:\\Program Files (x86)\\Windows Kits\\10\\References\\10.0.22621.0;C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\lib\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\lib\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\lib\\x86\\store\\references;C:\\Program Files (x86)\\Windows Kits\\10\\UnionMetadata\\10.0.22621.0;C:\\Program Files (x86)\\Windows Kits\\10\\References\\10.0.22621.0;C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319
      #$ LOCALAPPDATA=C:\\Users\\<USER>\\AppData\\Local
      #$ LOGONSERVER=\\\\KHANG
      #$ MOZ_PLUGIN_PATH=C:\\Program Files (x86)\\Nuance\\PDF Professional 7\\Bin\\
      #$ MVCAM_COMMON_RUNENV=C:\\Program Files (x86)\\MVS\\Development
      #$ MVCAM_GENICAM_CLPROTOCOL=C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\CLProtocol
      #$ MVCAM_GIGE_DEBUG_HEARTBEAT=60000
      #$ NETFXSDKDir=C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\
      #$ NUMBER_OF_PROCESSORS=12
      #$ OMP_NUM_THREADS=12
      #$ OneDrive=C:\\Users\\<USER>\\OneDrive
      #$ OneDriveConsumer=C:\\Users\\<USER>\\OneDrive
      #$ ORIGINAL_XDG_CURRENT_DESKTOP=undefined
      #$ OS=Windows_NT
      #$ Path=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn;C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\FSharp\\Tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64;C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn;C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\FSharp\\Tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\\\x86;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64;C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp;C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win32_i86;C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win64_x64;C:\\Program Files\\Basler\\pylon 8\\Runtime\\x64\\;C:\\Program Files\\Basler\\pylon 8\\Runtime\\Win32\\;C:\\Program Files (x86)\\Siemens\\Automation\\SCADA-RT_V11\\WinCC\\Bin;C:\\Program Files (x86)\\Common Files\\Siemens\\Bin;C:\\Program Files (x86)\\Common Files\\Siemens\\CommonArchiving;C:\\Program Files (x86)\\Common Files\\Siemens\\ACE\\Bin;C:\\Program Files (x86)\\IDMVS\\Applications\\Win64\\plugins\\MvSDK;C:\\Program Files (x86)\\IDMVS\\Applications\\Win32\\plugins\\MvSDK;C:\\Program Files\\Common Files\\Siemens\\Automation\\Simatic OAM\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\130\\Tools\\Binn\\;C:\\Program Files (x86)\\Microsoft SQL Server\\140\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\140\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\140\\DTS\\Binn\\;C:\\Program Files (x86)\\Common Files\\Siemens\\;C:\\Program Files (x86)\\Common Files\\Siemens\\Interfaces;C:\\Program Files (x86)\\Common Files\\Siemens\\ACE\\Interfaces;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files (x86)\\Siemens\\Automation\\SCADA-RT_V11\\WinCC\\Interfaces;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\CMake\\bin;C:\\Program Files\\nodejs\\;C:\\mingw64\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\opencv\\build\\x64\\vc16\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\ProgramData\\anaconda3\\envs\\khang1611;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Program Files\\JetBrains\\PyCharm 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.2.1\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg
      #$ PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
      #$ Platform=x64
      #$ PROCESSOR_ARCHITECTURE=AMD64
      #$ PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 186 Stepping 3, GenuineIntel
      #$ PROCESSOR_LEVEL=6
      #$ PROCESSOR_REVISION=ba03
      #$ ProgramData=C:\\ProgramData
      #$ ProgramFiles=C:\\Program Files
      #$ ProgramFiles(x86)=C:\\Program Files (x86)
      #$ ProgramW6432=C:\\Program Files
      #$ PROMPT=$P$G
      #$ PSModulePath=C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules;C:\\Program Files (x86)\\Microsoft SQL Server\\140\\Tools\\PowerShell\\Modules\\
      #$ PUBLIC=C:\\Users\\<USER>\\Program Files\\JetBrains\\PyCharm 2024.2.1\\bin;
      #$ PyCharm Community Edition=C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.1\\bin;
      #$ PYTHONPATH=C:\\Program Files (x86)\\MVS\\Development\\Samples\\Python\\MvImport
      #$ SESSIONNAME=Console
      #$ Simatic_OAM=C:\\Program Files\\Common Files\\Siemens\\Automation\\Simatic OAM
      #$ Simatic_OAM_DATA=C:\\ProgramData\\Siemens\\Automation\\Simatic OAM
      #$ SystemDrive=C:
      #$ SystemRoot=C:\\WINDOWS
      #$ UCRTVersion=10.0.22621.0
      #$ UniversalCRTSdkDir=C:\\Program Files (x86)\\Windows Kits\\10\\
      #$ USERDOMAIN=KHANG
      #$ USERDOMAIN_ROAMINGPROFILE=KHANG
      #$ USERNAME=khang
      #$ USERPROFILE=C:\\Users\\<USER>\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\
      #$ VCINSTALLDIR=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\
      #$ VCPKG_ROOT=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg
      #$ VCToolsInstallDir=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\
      #$ VCToolsRedistDir=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Redist\\MSVC\\14.40.33807\\
      #$ VCToolsVersion=14.41.34120
      #$ VisualStudioVersion=17.0
      #$ VS170COMNTOOLS=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\
      #$ VSCMD_ARG_app_plat=Desktop
      #$ VSCMD_ARG_HOST_ARCH=x64
      #$ VSCMD_ARG_TGT_ARCH=x64
      #$ VSCMD_VER=17.11.0
      #$ VSCODE_CODE_CACHE_PATH=C:\\Users\\<USER>\\AppData\\Roaming\\Code\\CachedData\\33fc5a94a3f99ebe7087e8fe79fbe1d37a251016
      #$ VSCODE_CRASH_REPORTER_PROCESS_TYPE=extensionHost
      #$ VSCODE_CWD=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code
      #$ VSCODE_ESM_ENTRYPOINT=vs/workbench/api/node/extensionHostProcess
      #$ VSCODE_HANDLES_UNCAUGHT_ERRORS=true
      #$ VSCODE_IPC_HOOK=\\\\.\\pipe\\1e7369d3-1.97.0-main-sock
      #$ VSCODE_NLS_CONFIG={"userLocale":"en-us","osLocale":"vi","resolvedLanguage":"en","defaultMessagesFile":"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Programs\\\\Microsoft VS Code\\\\resources\\\\app\\\\out\\\\nls.messages.json","locale":"en-us","availableLanguages":{}}
      #$ VSCODE_PID=15816
      #$ vsconsoleoutput=1
      #$ VSINSTALLDIR=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\
      #$ VSSDK150INSTALL=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VSSDK
      #$ VSSDKINSTALL=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VSSDK
      #$ windir=C:\\WINDOWS
      #$ WindowsLibPath=C:\\Program Files (x86)\\Windows Kits\\10\\UnionMetadata\\10.0.22621.0;C:\\Program Files (x86)\\Windows Kits\\10\\References\\10.0.22621.0
      #$ WindowsSdkBinPath=C:\\Program Files (x86)\\Windows Kits\\10\\bin\\
      #$ WindowsSdkDir=C:\\Program Files (x86)\\Windows Kits\\10\\
      #$ WindowsSDKLibVersion=10.0.22621.0\\
      #$ WindowsSdkVerBinPath=C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\
      #$ WindowsSDKVersion=10.0.22621.0\\
      #$ WindowsSDK_ExecutablePath_x64=C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\
      #$ WindowsSDK_ExecutablePath_x86=C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\
      #$ ZES_ENABLE_SYSMAN=1
      #$ __DOTNET_ADD_64BIT=1
      #$ __DOTNET_PREFERRED_BITNESS=64
      #$ __VSCMD_PREINIT_INCLUDE=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\include;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\include;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include;C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\um;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\shared;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\winrt;C:\\Program Files (x86)\\Windows Kits\\10\\\\include\\10.0.22621.0\\\\cppwinrt;C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um
      #$ __VSCMD_PREINIT_LIBPATH=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\ATLMFC\\lib\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\lib\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\lib\\x86\\store\\references;C:\\Program Files (x86)\\Windows Kits\\10\\UnionMetadata\\10.0.22621.0;C:\\Program Files (x86)\\Windows Kits\\10\\References\\10.0.22621.0;C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319
      #$ __VSCMD_PREINIT_PATH=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn;C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\FSharp\\Tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\\\x86;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64;C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp;C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win32_i86;C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win64_x64;C:\\Program Files\\Basler\\pylon 8\\Runtime\\x64\\;C:\\Program Files\\Basler\\pylon 8\\Runtime\\Win32\\;C:\\Program Files (x86)\\Siemens\\Automation\\SCADA-RT_V11\\WinCC\\Bin;C:\\Program Files (x86)\\Common Files\\Siemens\\Bin;C:\\Program Files (x86)\\Common Files\\Siemens\\CommonArchiving;C:\\Program Files (x86)\\Common Files\\Siemens\\ACE\\Bin;C:\\Program Files (x86)\\IDMVS\\Applications\\Win64\\plugins\\MvSDK;C:\\Program Files (x86)\\IDMVS\\Applications\\Win32\\plugins\\MvSDK;C:\\Program Files\\Common Files\\Siemens\\Automation\\Simatic OAM\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\130\\Tools\\Binn\\;C:\\Program Files (x86)\\Microsoft SQL Server\\140\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\140\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\140\\DTS\\Binn\\;C:\\Program Files (x86)\\Common Files\\Siemens\\;C:\\Program Files (x86)\\Common Files\\Siemens\\Interfaces;C:\\Program Files (x86)\\Common Files\\Siemens\\ACE\\Interfaces;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files (x86)\\Siemens\\Automation\\SCADA-RT_V11\\WinCC\\Interfaces;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\CMake\\bin;C:\\Program Files\\nodejs\\;C:\\mingw64\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\opencv\\build\\x64\\vc16\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\ProgramData\\anaconda3\\envs\\khang1611;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Program Files\\JetBrains\\PyCharm 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.2.1\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg
      #$ __VSCMD_PREINIT_VCToolsVersion=14.41.34120
      #$ __VSCMD_PREINIT_VS170COMNTOOLS=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools
      #$ PATH=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn;C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\FSharp\\Tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64;C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn;C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\FSharp\\Tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\\\x86;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64;C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp;C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win32_i86;C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win64_x64;C:\\Program Files\\Basler\\pylon 8\\Runtime\\x64\\;C:\\Program Files\\Basler\\pylon 8\\Runtime\\Win32\\;C:\\Program Files (x86)\\Siemens\\Automation\\SCADA-RT_V11\\WinCC\\Bin;C:\\Program Files (x86)\\Common Files\\Siemens\\Bin;C:\\Program Files (x86)\\Common Files\\Siemens\\CommonArchiving;C:\\Program Files (x86)\\Common Files\\Siemens\\ACE\\Bin;C:\\Program Files (x86)\\IDMVS\\Applications\\Win64\\plugins\\MvSDK;C:\\Program Files (x86)\\IDMVS\\Applications\\Win32\\plugins\\MvSDK;C:\\Program Files\\Common Files\\Siemens\\Automation\\Simatic OAM\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\130\\Tools\\Binn\\;C:\\Program Files (x86)\\Microsoft SQL Server\\140\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\140\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\140\\DTS\\Binn\\;C:\\Program Files (x86)\\Common Files\\Siemens\\;C:\\Program Files (x86)\\Common Files\\Siemens\\Interfaces;C:\\Program Files (x86)\\Common Files\\Siemens\\ACE\\Interfaces;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files (x86)\\Siemens\\Automation\\SCADA-RT_V11\\WinCC\\Interfaces;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\CMake\\bin;C:\\Program Files\\nodejs\\;C:\\mingw64\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\opencv\\build\\x64\\vc16\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\ProgramData\\anaconda3\\envs\\khang1611;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Program Files\\JetBrains\\PyCharm 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.2.1\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg
      #$ _NVVM_BRANCH_=nvvm
      #$ _SPACE_= 
      #$ _CUDART_=cudart
      #$ _HERE_=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin
      #$ _THERE_=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin
      #$ _TARGET_SIZE_=
      #$ _TARGET_DIR_=
      #$ _TARGET_SIZE_=64
      #$ _WIN_PLATFORM_=x64
      #$ TOP=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/..
      #$ CICC_PATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../nvvm/bin
      #$ NVVMIR_LIBRARY_DIR=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../nvvm/libdevice
      #$ PATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../nvvm/bin;C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin;C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../lib;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn;C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\x64\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\FSharp\\Tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\\\x64;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64;C:\\Windows\\Microsoft.NET\\Framework64\\v4.0.30319;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x64;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX86\\x86;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\VCPackages;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TestWindow;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\TeamFoundation\\Team Explorer;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\bin\\Roslyn;C:\\Program Files (x86)\\Microsoft SDKs\\Windows\\v10.0A\\bin\\NETFX 4.8 Tools\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\FSharp\\Tools;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Team Tools\\DiagnosticsHub\\Collector;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\\\x86;C:\\Program Files (x86)\\Windows Kits\\10\\bin\\\\x86;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\\\MSBuild\\Current\\Bin\\amd64;C:\\Windows\\Microsoft.NET\\Framework\\v4.0.30319;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.8\\libnvvp;C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win32_i86;C:\\Program Files (x86)\\Common Files\\MVS\\Runtime\\Win64_x64;C:\\Program Files\\Basler\\pylon 8\\Runtime\\x64\\;C:\\Program Files\\Basler\\pylon 8\\Runtime\\Win32\\;C:\\Program Files (x86)\\Siemens\\Automation\\SCADA-RT_V11\\WinCC\\Bin;C:\\Program Files (x86)\\Common Files\\Siemens\\Bin;C:\\Program Files (x86)\\Common Files\\Siemens\\CommonArchiving;C:\\Program Files (x86)\\Common Files\\Siemens\\ACE\\Bin;C:\\Program Files (x86)\\IDMVS\\Applications\\Win64\\plugins\\MvSDK;C:\\Program Files (x86)\\IDMVS\\Applications\\Win32\\plugins\\MvSDK;C:\\Program Files\\Common Files\\Siemens\\Automation\\Simatic OAM\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\130\\Tools\\Binn\\;C:\\Program Files (x86)\\Microsoft SQL Server\\140\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\140\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\140\\DTS\\Binn\\;C:\\Program Files (x86)\\Common Files\\Siemens\\;C:\\Program Files (x86)\\Common Files\\Siemens\\Interfaces;C:\\Program Files (x86)\\Common Files\\Siemens\\ACE\\Interfaces;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files (x86)\\Siemens\\Automation\\SCADA-RT_V11\\WinCC\\Interfaces;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\CMake\\bin;C:\\Program Files\\nodejs\\;C:\\mingw64\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.1.0\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\opencv\\build\\x64\\vc16\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\ProgramData\\anaconda3\\envs\\khang1611;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Program Files\\JetBrains\\PyCharm 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\PyCharm Community Edition 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.2.1\\bin;;C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.2.1\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\Ninja;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\VC\\Linux\\bin\\ConnectionManagerExe;C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\vcpkg
      #$ INCLUDES="-IC:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../include"  
      #$ LIBRARIES=  "/LIBPATH:C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../lib/x64"
      #$ CUDAFE_FLAGS=
      #$ PTXAS_FLAGS=
      nvcc fatal   : Don't know what to do with '__cmake_determine_cuda'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDAToolkit.cmake:685 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDAToolkit.cmake:802 (_CUDAToolkit_find_root_dir)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      Parsed CUDAToolkit nvcc location:
      C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDAToolkit.cmake:697 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDAToolkit.cmake:802 (_CUDAToolkit_find_root_dir)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      Parsed CUDAToolkit nvcc implicit include information:
      C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/include
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDAToolkit.cmake:712 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindCUDAToolkit.cmake:802 (_CUDAToolkit_find_root_dir)"
      - "CMakeLists.txt:9 (find_package)"
    message: |
      Parsed CUDAToolkit nvcc implicit link information:
        link line regex: [^( *|.*[/\\])(link\\.exe|;ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        link line: [cuda-fake-ld   "/LIBPATH:C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../lib/x64"]
          arg [cuda-fake-ld] ==> ignore
          arg [/LIBPATH:C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../lib/x64] ==> dir [C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../lib/x64]
        collapse library dir [C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/bin/../lib/x64] ==> [C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64]
        implicit libs: []
        implicit objs: []
        implicit dirs: [C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64]
        implicit fwks: []
      
      C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.8/lib/x64
      
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: /DWIN32;/D_WINDOWS;/W3
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.12.12+1cce77968 for .NET Framework
      Build started 2/16/2025 10:46:56 PM.
      
      Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX86\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\Hostx86\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:05.20
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: /DWIN32;/D_WINDOWS;/W3;/GR;/EHsc
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.12.12+1cce77968 for .NET Framework
      Build started 2/16/2025 10:47:03 PM.
      
      Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX86\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\Hostx86\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:02.84
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iv5bcx"
      binary: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iv5bcx"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iv5bcx'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e75dc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.12.12+1cce77968 for .NET Framework
        Build started 2/16/2025 10:47:08 PM.
        
        Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iv5bcx\\cmTC_e75dc.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e75dc.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iv5bcx\\Debug\\".
          Creating directory "cmTC_e75dc.dir\\Debug\\cmTC_e75dc.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e75dc.dir\\Debug\\cmTC_e75dc.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e75dc.dir\\Debug\\cmTC_e75dc.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX86\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e75dc.dir\\Debug\\\\" /Fd"cmTC_e75dc.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34438 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e75dc.dir\\Debug\\\\" /Fd"cmTC_e75dc.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iv5bcx\\Debug\\cmTC_e75dc.exe" /INCREMENTAL /ILK:"cmTC_e75dc.dir\\Debug\\cmTC_e75dc.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iv5bcx/Debug/cmTC_e75dc.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iv5bcx/Debug/cmTC_e75dc.lib" /MACHINE:X64  /machine:x64 cmTC_e75dc.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_e75dc.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iv5bcx\\Debug\\cmTC_e75dc.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_e75dc.dir\\Debug\\cmTC_e75dc.tlog\\unsuccessfulbuild".
          Touching "cmTC_e75dc.dir\\Debug\\cmTC_e75dc.tlog\\cmTC_e75dc.lastbuildstate".
        Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iv5bcx\\cmTC_e75dc.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:04.31
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.42.34433/bin/HostX86/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.42.34433/bin/HostX86/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.42.34438.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-y0dpc9"
      binary: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-y0dpc9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-y0dpc9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ba914.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.12.12+1cce77968 for .NET Framework
        Build started 2/16/2025 10:47:15 PM.
        
        Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y0dpc9\\cmTC_ba914.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_ba914.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y0dpc9\\Debug\\".
          Creating directory "cmTC_ba914.dir\\Debug\\cmTC_ba914.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_ba914.dir\\Debug\\cmTC_ba914.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_ba914.dir\\Debug\\cmTC_ba914.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX86\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_ba914.dir\\Debug\\\\" /Fd"cmTC_ba914.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34438 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_ba914.dir\\Debug\\\\" /Fd"cmTC_ba914.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.42.34433\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y0dpc9\\Debug\\cmTC_ba914.exe" /INCREMENTAL /ILK:"cmTC_ba914.dir\\Debug\\cmTC_ba914.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-y0dpc9/Debug/cmTC_ba914.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-y0dpc9/Debug/cmTC_ba914.lib" /MACHINE:X64  /machine:x64 cmTC_ba914.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_ba914.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y0dpc9\\Debug\\cmTC_ba914.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_ba914.dir\\Debug\\cmTC_ba914.tlog\\unsuccessfulbuild".
          Touching "cmTC_ba914.dir\\Debug\\cmTC_ba914.tlog\\cmTC_ba914.lastbuildstate".
        Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y0dpc9\\cmTC_ba914.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:03.13
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.42.34433/bin/HostX86/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.42.34433/bin/HostX86/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.42.34438.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: /DWIN32;/D_WINDOWS;/W3
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.9+e0f243f1e for .NET Framework
      Build started 2/18/2025 10:14:21 PM.
      
      Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX86\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx86\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:04.34
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: /DWIN32;/D_WINDOWS;/W3;/GR;/EHsc
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.13.9+e0f243f1e for .NET Framework
      Build started 2/18/2025 10:14:26 PM.
      
      Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX86\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx86\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\3.31.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:03.05
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/DO_AN_TN/MainForm/build/CMakeFiles/3.31.4/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-uh0z8c"
      binary: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-uh0z8c"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-uh0z8c'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_cc342.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.9+e0f243f1e for .NET Framework
        Build started 2/18/2025 10:14:31 PM.
        
        Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uh0z8c\\cmTC_cc342.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_cc342.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uh0z8c\\Debug\\".
          Creating directory "cmTC_cc342.dir\\Debug\\cmTC_cc342.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_cc342.dir\\Debug\\cmTC_cc342.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_cc342.dir\\Debug\\cmTC_cc342.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX86\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_cc342.dir\\Debug\\\\" /Fd"cmTC_cc342.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34808 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_cc342.dir\\Debug\\\\" /Fd"cmTC_cc342.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uh0z8c\\Debug\\cmTC_cc342.exe" /INCREMENTAL /ILK:"cmTC_cc342.dir\\Debug\\cmTC_cc342.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-uh0z8c/Debug/cmTC_cc342.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-uh0z8c/Debug/cmTC_cc342.lib" /MACHINE:X64  /machine:x64 cmTC_cc342.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_cc342.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uh0z8c\\Debug\\cmTC_cc342.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_cc342.dir\\Debug\\cmTC_cc342.tlog\\unsuccessfulbuild".
          Touching "cmTC_cc342.dir\\Debug\\cmTC_cc342.tlog\\cmTC_cc342.lastbuildstate".
        Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uh0z8c\\cmTC_cc342.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:03.18
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX86/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX86/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34808.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iz51ao"
      binary: "C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iz51ao"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iz51ao'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_7c58f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.13.9+e0f243f1e for .NET Framework
        Build started 2/18/2025 10:14:36 PM.
        
        Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iz51ao\\cmTC_7c58f.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_7c58f.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iz51ao\\Debug\\".
          Creating directory "cmTC_7c58f.dir\\Debug\\cmTC_7c58f.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_7c58f.dir\\Debug\\cmTC_7c58f.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_7c58f.dir\\Debug\\cmTC_7c58f.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX86\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_7c58f.dir\\Debug\\\\" /Fd"cmTC_7c58f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.43.34808 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_7c58f.dir\\Debug\\\\" /Fd"cmTC_7c58f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX86\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iz51ao\\Debug\\cmTC_7c58f.exe" /INCREMENTAL /ILK:"cmTC_7c58f.dir\\Debug\\cmTC_7c58f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iz51ao/Debug/cmTC_7c58f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/DO_AN_TN/MainForm/build/CMakeFiles/CMakeScratch/TryCompile-iz51ao/Debug/cmTC_7c58f.lib" /MACHINE:X64  /machine:x64 cmTC_7c58f.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_7c58f.vcxproj -> C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iz51ao\\Debug\\cmTC_7c58f.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_7c58f.dir\\Debug\\cmTC_7c58f.tlog\\unsuccessfulbuild".
          Touching "cmTC_7c58f.dir\\Debug\\cmTC_7c58f.tlog\\cmTC_7c58f.lastbuildstate".
        Done Building Project "C:\\DO_AN_TN\\MainForm\\build\\CMakeFiles\\CMakeScratch\\TryCompile-iz51ao\\cmTC_7c58f.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:02.78
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX86/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.43.34808/bin/HostX86/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34808.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
