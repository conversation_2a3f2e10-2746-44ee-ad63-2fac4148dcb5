# -- coding: utf-8 --
import msvcrt
import numpy as np
import datetime
import inspect
import random
import sys
import cv2
import icon_rc
import snap7
import time
import os
import threading
from threading import Lock
import logging
import pypylon.pylon as pylon
from PyQt5.QtGui import  *
from PyQt5.QtCore import *
from ultralytics import YOLO
from ctypes import *
from PyQt5.QtWidgets import *
from pyzbar.pyzbar import decode
from MvCameraControl_class import *
from MvErrorDefine_const import *
from CameraParams_header import *
from snap7.util import get_bool, get_real, set_bool, get_int, set_int
from UI_form import Ui_MainWindow
from ConnectPLC_TCIP import PLC_Thread
from ConnectHIKVision import CameraOperation

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
sys.path.append("C:/Users/<USER>/OneDrive/Dokumen/GitHub/Python/MvImport")

class MainWindow(QMainWindow):
    data_input_signal = pyqtSignal(int, int, int)
    def __init__(self):
        super().__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        self.plc_controller = PLC_Thread()
        self.camera_controller = CameraOperation(obj_cam = None, st_device_list = None) 

        self.setWindowTitle("Vision and Delta Control System")
        self.setWindowIcon(QIcon(":/icon/eye.png"))

        self.data_input_signal.connect(self.plc_controller.send_input_to_plc)
        
        global deviceList
        deviceList = MV_CC_DEVICE_INFO_LIST()
        global cam
        cam = MvCamera()
        global nSelCamIndex
        nSelCamIndex = 0
        global obj_cam_operation
        obj_cam_operation = 0
        global isOpen
        isOpen = False
        global isGrabbing
        isGrabbing = False
        global isCalibMode 
        isCalibMode = True
        global checkin_programPLC 
        checkin_programPLC = False


        self.ui.slide_menu_container.hide()
        self.ui.stackedWidget.setCurrentIndex(0)

        self.ui.btn_menu.setChecked(True)
        self.ui.btn_Home.clicked.connect(self.on_btn_Home_clicked)
        self.ui.btn_Manual.clicked.connect(self.on_btn_Manual_clicked)
        self.ui.btn_Setting.clicked.connect(self.on_btn_Setting_clicked)
        self.ui.btn_User.clicked.connect(self.on_btn_User_clicked)
        self.ui.btn_exit.clicked.connect(self.on_btn_exit_clicked)
        self.ui.btn_menu.clicked.connect(self.on_btn_menu_clicked)

        self.ui.btn_Scan.clicked.connect(self.enum_devices)
        self.ui.bnOpen.clicked.connect(self.open_device)
        self.ui.bnStart.clicked.connect(self.start_grabbing)
        self.ui.bnClose.clicked.connect(self.close_device)
        self.ui.btn_ConnectCamera.clicked.connect(self.start_camera)
        self.ui.bnStop.clicked.connect(self.stop_grabbing)
        self.ui.btn_RUN.clicked.connect(self.print_input_data)

        self.ui.btn_ConnectPLC.clicked.connect(self.start_plc_thread)
        self.ui.btn_START.clicked.connect(self.plc_controller.START_Mode)
        self.ui.btn_START_2.clicked.connect(self.plc_controller.START_Mode)
        self.ui.btn_STOP.clicked.connect(self.plc_controller.STOP_Mode)
        self.ui.btn_STOP_2.clicked.connect(self.plc_controller.STOP_Mode)
        self.ui.btn_RESET.clicked.connect(self.plc_controller.RESET_Mode)
        self.ui.btn_RESET_2.clicked.connect(self.plc_controller.RESET_Mode)
        self.ui.btn_HOME.clicked.connect(self.plc_controller.HOME_Mode)
        self.ui.btn_HOME_2.clicked.connect(self.plc_controller.HOME_Mode)

        self.ui.bnSoftwareTrigger.clicked.connect(self.trigger_once)
        self.ui.radioTriggerMode.clicked.connect(self.set_software_trigger_mode)
        self.ui.radioContinueMode.clicked.connect(self.set_continue_mode)

        self.ui.bnGetParam.clicked.connect(self.get_param)
        self.ui.bnSetParam.clicked.connect(self.set_param)
        self.ui.bnSaveImage.clicked.connect(self.save_bmp)


        
    def xFunc(self, event):
        global nSelCamIndex
        nSelCamIndex = self.TxtWrapBy("[", "]", self.ui.ComboDevices.get())

    def decoding_char(self, c_ubyte_value):
        c_char_p_value = ctypes.cast(c_ubyte_value, ctypes.c_char_p)
        try:
            decode_str = c_char_p_value.value.decode('gbk') 
        except UnicodeDecodeError:
            decode_str = str(c_char_p_value.value)
        return decode_str
    
    def enum_devices(self):
        global deviceList
        global obj_cam_operation

        deviceList = MV_CC_DEVICE_INFO_LIST()
        ret = MvCamera.MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, deviceList)
        if ret != 0:
            strError = "Enum devices fail! ret = :" + self.ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
            return ret

        if deviceList.nDeviceNum == 0:
            QMessageBox.warning(self, "Info", "Find no device", QMessageBox.Ok)
            return ret
        print("Find %d devices!" % deviceList.nDeviceNum)

        devList = []
        for i in range(0, deviceList.nDeviceNum):
            mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
            if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE:
                print("\ngige device: [%d]" % i)
                user_defined_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chUserDefinedName)
                model_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName)
                print("device user define name: " + user_defined_name)
                print("device model name: " + model_name)

                nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
                nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
                nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
                nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
                print("current ip: %d.%d.%d.%d " % (nip1, nip2, nip3, nip4))
                self.ui.lb_ipcamera.setText(str(nip1) + "." + str(nip2) + "." + str(nip3) + "." + str(nip4))
                devList.append(
                    "[" + str(i) + "]GigE: " + user_defined_name + " " + model_name + "(" + str(nip1) + "." + str(
                        nip2) + "." + str(nip3) + "." + str(nip4) + ")")
            elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
                print("\nu3v device: [%d]" % i)
                user_defined_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chUserDefinedName)
                model_name = self.decoding_char(mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName)
                print("device user define name: " + user_defined_name)
                print("device model name: " + model_name)

                strSerialNumber = ""
                for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                    if per == 0:
                        break
                    strSerialNumber = strSerialNumber + chr(per)
                print("user serial number: " + strSerialNumber)
                devList.append("[" + str(i) + "]USB: " + user_defined_name + " " + model_name
                               + "(" + str(strSerialNumber) + ")")

        self.ui.ComboDevices.clear()
        self.ui.ComboDevices.addItems(devList)
        self.ui.ComboDevices.setCurrentIndex(0)
    
    def open_device(self):
        global deviceList
        global nSelCamIndex
        global obj_cam_operation
        global isOpen

        if isOpen:
            QMessageBox.warning(self, "Error", 'Camera is Running!', QMessageBox.Ok)
            return MV_E_CALLORDER

        nSelCamIndex = self.ui.ComboDevices.currentIndex()
        if nSelCamIndex < 0:
            QMessageBox.warning(self, "Error", 'Please select a camera!', QMessageBox.Ok)
            return MV_E_CALLORDER

        obj_cam_operation = CameraOperation(cam, deviceList, nSelCamIndex)
        ret = obj_cam_operation.Open_device()
        if 0 != ret:
            strError = "Open device failed ret:" + self.ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
            isOpen = False
        else:
            self.set_continue_mode()
            self.get_param()
            isOpen = True
            self.ui.label_text.setText("Open device successfully!")
            self.enable_controls()
    
    def start_grabbing(self):
        global obj_cam_operation
        global isGrabbing

        ret = obj_cam_operation.Start_grabbing(self.ui.widgetDisplay, self.ui.tableWidget_out)
        if ret != 0:
            strError = "Start grabbing failed ret:" + self.ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            isGrabbing = True
            self.ui.label_text.setText("Start grabbing successfully!")
            self.enable_controls()
    
    def start_camera(self):
        global isGrabbing 
        isGrabbing = True 

        print ("Start all")
        self.enum_devices()
        self.open_device()
        self.start_grabbing()
        self.start_cam_thread()

    def stop_grabbing(self):
        global obj_cam_operation
        global isGrabbing

        ret = obj_cam_operation.Stop_grabbing()
        if ret != 0:
            strError = "Stop grabbing failed ret:" + self.ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            isGrabbing = False
            self.ui.label_text.setText("Stop grabbing successfully!")
            self.enable_controls()
    
    def close_device(self):
        global isOpen
        global isGrabbing
        global obj_cam_operation

        if isOpen:
            obj_cam_operation.Close_device()
            isOpen = False

        isGrabbing = False
        self.enable_controls()
    
    def set_continue_mode(self):
        strError = None
        trigger_mode_enabled = False

        ret = obj_cam_operation.Set_trigger_mode(False)
        if ret != 0:
            strError = "Set continue mode failed ret:" + self.ToHexStr(ret) + " mode is " + str(trigger_mode_enabled)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            self.ui.radioContinueMode.setChecked(True)
            self.ui.radioTriggerMode.setChecked(False)
            self.ui.bnSoftwareTrigger.setEnabled(False)
    
    def set_software_trigger_mode(self):

        ret = obj_cam_operation.Set_trigger_mode(True)
        if ret != 0:
            strError = "Set trigger mode failed ret:" + self.ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            self.ui.radioContinueMode.setChecked(False)
            self.ui.radioTriggerMode.setChecked(True)
            self.ui.bnSoftwareTrigger.setEnabled(isGrabbing)

    def trigger_once(self):
        ret = obj_cam_operation.Trigger_once()
        if ret != 0:
            strError = "TriggerSoftware failed ret:" + self.ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
    
    def save_bmp(self):
        ret = obj_cam_operation.Save_Bmp()
        if ret != MV_OK:
            strError = "Save BMP failed ret:" + self.ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            print("Save image success")
            
    def is_float(str):
        try:
            float(str)
            return True
        except ValueError:
            return False
    
    def get_param(self):
        ret = obj_cam_operation.Get_parameter()
        if ret != MV_OK:
            strError = "Get param failed ret:" + self.ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
        else:
            self.ui.edtExposureTime.setText("{0:.2f}".format(obj_cam_operation.exposure_time))
            self.ui.edtGain.setText("{0:.2f}".format(obj_cam_operation.gain))
            self.ui.edtFrameRate.setText("{0:.2f}".format(obj_cam_operation.frame_rate))

    def set_param(self):
        frame_rate = self.ui.edtFrameRate.text()
        exposure = self.ui.edtExposureTime.text()
        gain = self.ui.edtGain.text()

        if self.is_float(frame_rate)!=True or self.is_float(exposure)!=True or self.is_float(gain)!=True:
            strError = "Set param failed ret:" + self.ToHexStr(MV_E_PARAMETER)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)
            return MV_E_PARAMETER
        
        ret = obj_cam_operation.Set_parameter(frame_rate, exposure, gain)
        if ret != MV_OK:
            strError = "Set param failed ret:" + self.ToHexStr(ret)
            QMessageBox.warning(self, "Error", strError, QMessageBox.Ok)

        return MV_OK

    def enable_controls(self):
        global isGrabbing
        global isOpen

        self.ui.bnOpen.setEnabled(not isOpen)
        self.ui.bnClose.setEnabled(isOpen)
        self.ui.btn_ConnectCamera.setEnabled(isOpen and (not isGrabbing))
        self.ui.bnStop.setEnabled(isOpen and isGrabbing)
        self.ui.bnSoftwareTrigger.setEnabled(isGrabbing and self.ui.radioTriggerMode.isChecked())
        self.ui.bnSaveImage.setEnabled(isOpen and isGrabbing)
    
    def on_btn_menu_clicked(self, checked):
        if checked:
            self.ui.slide_menu_container.show()
        else:
            self.ui.slide_menu_container.hide()
    def on_btn_Home_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(0)
    def on_btn_Manual_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(1)
    def on_btn_Setting_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(2)
    def on_btn_User_clicked(self):
        self.ui.stackedWidget.setCurrentIndex(3)
    
    def on_btn_exit_clicked(self):
        self.close()
        self.close_plc()
        self.close_device()
        print("Exit Program")
        sys.exit()
    
    def closeEvent(self, event):
        reply = QMessageBox.question(self, "Exit", "Are you sure to exit?",
                                 QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
    
    def close_plc(self):
        self.plc_controller.stop()
        self.plc_controller.terminate()
        self.plc_controller.wait()

    def start_cam_thread(self):
        self.camera_controller.my_signal.connect(self.handle_message)
        self.camera_controller.start()

    def handle_message(self, message):
        print(f"Receiver: Nhận được tín hiệu: {message}")
    
    def start_plc_thread(self):
        self.plc_controller.update_plc_data_signal.connect(self.update_plc_data)
        self.plc_controller.error.connect(self.Error_PLC)
        self.plc_controller.connected.connect(self.on_plc_connected)
        self.plc_controller.start()

    def on_plc_connected(self, connected):
        if connected:
            self.ui.label_8.setText("PLC Siemens S7-1200")
            self.ui.label_11.setText(self.plc_controller.ip)
            self.ui.label_12.setText("Connected to PLC Successfully!")
            self.ui.system_active.setStyleSheet("background-color: green; color: white; font-size: 16px;")
            self.ui.system_active.setText("System: ACTIVE")
        else:
            QMessageBox.warning(self,"PLC Connection", "Failed to connect to PLC.")
            self.ui.label_12.setText("Failed to connect to PLC.")
            self.ui.system_active.setStyleSheet("background-color: red; color: white; font-size: 16px;")
            self.ui.system_active.setText("System: INACTIVE")

    @pyqtSlot(float, float, float, float, float, float, float, float, int, int, int, int)
    def update_plc_data(self, delta_x, delta_y, delta_z, axis_x, axis_y, axis_z, tbTocdoBT, tbTocdoRB, Pos_X, Pos_Y, SLuong, number):
        self.ui.tbThetaX.setText(f"{delta_x:.2f}")
        self.ui.tbThetaY.setText(f"{delta_y:.2f}")
        self.ui.tbThetaZ.setText(f"{delta_z:.2f}")
        self.ui.tbAxisX.setText(f"{axis_x:.2f}")
        self.ui.tbAxisY.setText(f"{axis_y:.2f}")
        self.ui.tbAxisZ.setText(f"{axis_z:.2f}")
        self.ui.lTocdoBT.setText(f"{tbTocdoBT:.2f}")
        self.ui.lTocdoRB.setText(f"{tbTocdoRB:.2f}")
        self.ui.Pos_X.setText(str(Pos_X))
        self.ui.Pos_Y.setText(str(Pos_Y))
        self.ui.label_SL.setText(str(SLuong))
        self.ui.label_loaiSP.setText(str(number))
    
    def print_input_data(self):
        try:
            data_X = int(self.ui.lineEdit_TD_X.text().strip())
            data_Y = int(self.ui.lineEdit_TD_Y.text().strip())
            data_Z = int(self.ui.lineEdit_TD_Z.text().strip())
            
            if 10 <= data_X <= 100 and 10 <= data_Y <= 100 and -450 <= data_Z <= -350:
                print(f"Nhập tọa độ X: {data_X}")
                print(f"Nhập tọa độ Y: {data_Y}")
                print(f"Nhập tọa độ Z: {data_Z}")
                self.data_input_signal.emit(int(data_X), int(data_Y), int(data_Z))
            else:
                QMessageBox.warning(self, "Error", "Data must be within the allowed range.")
        except Exception as e:
            print(f"Error: {e}")
            QMessageBox.warning(self, "Error", "Please enter a valid number.")


    def Error_PLC(self, error):
        QMessageBox.warning(self, "PLC Error", error)
        self.ui.label_error.setText(error)

def main():
    app = QApplication(sys.argv)
    mainWindow = MainWindow()
    mainWindow.show()
    sys.exit(app.exec_())
        
if __name__ == '__main__':
    main()