# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ScaleSYS.ui'
#
# Created by: PyQt5 UI code generator 5.15.10
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(2022, 1032)
        MainWindow.setMinimumSize(QtCore.QSize(1850, 1000))
        MainWindow.setSizeIncrement(QtCore.QSize(0, 0))
        MainWindow.setMouseTracking(False)
        MainWindow.setAutoFillBackground(False)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayoutWidget_2 = QtWidgets.QWidget(self.centralwidget)
        self.verticalLayoutWidget_2.setGeometry(QtCore.QRect(0, 90, 1881, 761))
        self.verticalLayoutWidget_2.setObjectName("verticalLayoutWidget_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_2)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.groupBox_6 = QtWidgets.QGroupBox(self.verticalLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.groupBox_6.setFont(font)
        self.groupBox_6.setAutoFillBackground(False)
        self.groupBox_6.setObjectName("groupBox_6")
        self.lbImage = QtWidgets.QLabel(self.groupBox_6)
        self.lbImage.setGeometry(QtCore.QRect(240, 10, 471, 551))
        self.lbImage.setStyleSheet("background-color: rgb(255, 255, 255);")
        self.lbImage.setFrameShape(QtWidgets.QFrame.WinPanel)
        self.lbImage.setText("")
        self.lbImage.setPixmap(QtGui.QPixmap("C:/Users/<USER>/Downloads/kisspng-delta-robot-parallel-manipulator-robotics-photo-printer-5adf2d537ceb15.8405795115245755715117.png"))
        self.lbImage.setScaledContents(True)
        self.lbImage.setAlignment(QtCore.Qt.AlignCenter)
        self.lbImage.setObjectName("lbImage")
        self.verticalLayoutWidget_3 = QtWidgets.QWidget(self.groupBox_6)
        self.verticalLayoutWidget_3.setGeometry(QtCore.QRect(0, 560, 931, 201))
        self.verticalLayoutWidget_3.setObjectName("verticalLayoutWidget_3")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_3)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.groupBox_3 = QtWidgets.QGroupBox(self.verticalLayoutWidget_3)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.groupBox_3.setFont(font)
        self.groupBox_3.setObjectName("groupBox_3")
        self.SLDetect = QtWidgets.QSlider(self.groupBox_3)
        self.SLDetect.setGeometry(QtCore.QRect(230, 40, 551, 22))
        self.SLDetect.setOrientation(QtCore.Qt.Horizontal)
        self.SLDetect.setObjectName("SLDetect")
        self.Slider_2 = QtWidgets.QSlider(self.groupBox_3)
        self.Slider_2.setGeometry(QtCore.QRect(230, 80, 551, 22))
        self.Slider_2.setOrientation(QtCore.Qt.Horizontal)
        self.Slider_2.setObjectName("Slider_2")
        self.label = QtWidgets.QLabel(self.groupBox_3)
        self.label.setGeometry(QtCore.QRect(20, 40, 161, 31))
        self.label.setObjectName("label")
        self.label_2 = QtWidgets.QLabel(self.groupBox_3)
        self.label_2.setGeometry(QtCore.QRect(20, 80, 81, 31))
        self.label_2.setObjectName("label_2")
        self.label_3 = QtWidgets.QLabel(self.groupBox_3)
        self.label_3.setGeometry(QtCore.QRect(20, 130, 131, 31))
        self.label_3.setObjectName("label_3")
        self.label_5 = QtWidgets.QLabel(self.groupBox_3)
        self.label_5.setGeometry(QtCore.QRect(220, 100, 31, 61))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_5.setFont(font)
        self.label_5.setObjectName("label_5")
        self.lineEdit = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit.setGeometry(QtCore.QRect(250, 120, 113, 20))
        font = QtGui.QFont()
        font.setPointSize(12)
        self.lineEdit.setFont(font)
        self.lineEdit.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEdit.setObjectName("lineEdit")
        self.lineEdit_2 = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_2.setGeometry(QtCore.QRect(500, 120, 113, 20))
        font = QtGui.QFont()
        font.setPointSize(12)
        self.lineEdit_2.setFont(font)
        self.lineEdit_2.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEdit_2.setObjectName("lineEdit_2")
        self.label_6 = QtWidgets.QLabel(self.groupBox_3)
        self.label_6.setGeometry(QtCore.QRect(470, 100, 31, 61))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_6.setFont(font)
        self.label_6.setObjectName("label_6")
        self.label_10 = QtWidgets.QLabel(self.groupBox_3)
        self.label_10.setGeometry(QtCore.QRect(760, 100, 31, 61))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_10.setFont(font)
        self.label_10.setObjectName("label_10")
        self.lineEdit_3 = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_3.setGeometry(QtCore.QRect(790, 120, 113, 20))
        self.lineEdit_3.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEdit_3.setObjectName("lineEdit_3")
        self.label_16 = QtWidgets.QLabel(self.groupBox_3)
        self.label_16.setGeometry(QtCore.QRect(220, 130, 31, 61))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_16.setFont(font)
        self.label_16.setObjectName("label_16")
        self.lineEdit_4 = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_4.setGeometry(QtCore.QRect(500, 150, 113, 20))
        self.lineEdit_4.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEdit_4.setObjectName("lineEdit_4")
        self.lineEdit_5 = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_5.setGeometry(QtCore.QRect(790, 150, 113, 20))
        self.lineEdit_5.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEdit_5.setObjectName("lineEdit_5")
        self.label_18 = QtWidgets.QLabel(self.groupBox_3)
        self.label_18.setGeometry(QtCore.QRect(470, 130, 31, 61))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_18.setFont(font)
        self.label_18.setObjectName("label_18")
        self.lineEdit_6 = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_6.setGeometry(QtCore.QRect(250, 150, 113, 20))
        font = QtGui.QFont()
        font.setPointSize(12)
        self.lineEdit_6.setFont(font)
        self.lineEdit_6.setText("")
        self.lineEdit_6.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEdit_6.setObjectName("lineEdit_6")
        self.label_19 = QtWidgets.QLabel(self.groupBox_3)
        self.label_19.setGeometry(QtCore.QRect(760, 130, 31, 61))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.label_19.setFont(font)
        self.label_19.setObjectName("label_19")
        self.numPH = QtWidgets.QLabel(self.groupBox_3)
        self.numPH.setGeometry(QtCore.QRect(810, 20, 71, 61))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.numPH.setFont(font)
        self.numPH.setObjectName("numPH")
        self.numDS = QtWidgets.QLabel(self.groupBox_3)
        self.numDS.setGeometry(QtCore.QRect(810, 60, 61, 61))
        font = QtGui.QFont()
        font.setPointSize(15)
        self.numDS.setFont(font)
        self.numDS.setObjectName("numDS")
        self.label_33 = QtWidgets.QLabel(self.groupBox_3)
        self.label_33.setGeometry(QtCore.QRect(290, 170, 471, 31))
        self.label_33.setObjectName("label_33")
        self.verticalLayout_3.addWidget(self.groupBox_3)
        self.tbX = QtWidgets.QLineEdit(self.groupBox_6)
        self.tbX.setGeometry(QtCore.QRect(40, 500, 111, 61))
        font = QtGui.QFont()
        font.setFamily("Bahnschrift SemiBold")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.tbX.setFont(font)
        self.tbX.setStyleSheet("background-color: rgb(255, 170, 127);")
        self.tbX.setAlignment(QtCore.Qt.AlignCenter)
        self.tbX.setReadOnly(True)
        self.tbX.setObjectName("tbX")
        self.label_15 = QtWidgets.QLabel(self.groupBox_6)
        self.label_15.setGeometry(QtCore.QRect(20, 460, 151, 41))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.label_15.setFont(font)
        self.label_15.setFrameShape(QtWidgets.QFrame.Box)
        self.label_15.setObjectName("label_15")
        self.tbY = QtWidgets.QLineEdit(self.groupBox_6)
        self.tbY.setGeometry(QtCore.QRect(780, 500, 111, 61))
        font = QtGui.QFont()
        font.setFamily("Bahnschrift SemiBold")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.tbY.setFont(font)
        self.tbY.setStyleSheet("background-color: rgb(255, 170, 127);")
        self.tbY.setAlignment(QtCore.Qt.AlignCenter)
        self.tbY.setReadOnly(True)
        self.tbY.setObjectName("tbY")
        self.btXPlus = QtWidgets.QPushButton(self.groupBox_6)
        self.btXPlus.setGeometry(QtCore.QRect(730, 30, 191, 71))
        font = QtGui.QFont()
        font.setPointSize(17)
        font.setBold(True)
        font.setWeight(75)
        self.btXPlus.setFont(font)
        self.btXPlus.setAutoFillBackground(False)
        self.btXPlus.setStyleSheet("background-color: rgb(170, 170, 127);")
        self.btXPlus.setCheckable(False)
        self.btXPlus.setAutoDefault(False)
        self.btXPlus.setObjectName("btXPlus")
        self.btYMinus = QtWidgets.QPushButton(self.groupBox_6)
        self.btYMinus.setGeometry(QtCore.QRect(730, 340, 191, 71))
        font = QtGui.QFont()
        font.setPointSize(17)
        font.setBold(True)
        font.setWeight(75)
        self.btYMinus.setFont(font)
        self.btYMinus.setAutoFillBackground(False)
        self.btYMinus.setStyleSheet("background-color: rgb(170, 170, 127);")
        self.btYMinus.setCheckable(False)
        self.btYMinus.setAutoDefault(False)
        self.btYMinus.setObjectName("btYMinus")
        self.btXMinus = QtWidgets.QPushButton(self.groupBox_6)
        self.btXMinus.setGeometry(QtCore.QRect(730, 120, 191, 71))
        font = QtGui.QFont()
        font.setPointSize(17)
        font.setBold(True)
        font.setWeight(75)
        self.btXMinus.setFont(font)
        self.btXMinus.setAutoFillBackground(False)
        self.btXMinus.setStyleSheet("background-color: rgb(170, 170, 127);")
        self.btXMinus.setCheckable(False)
        self.btXMinus.setAutoDefault(False)
        self.btXMinus.setObjectName("btXMinus")
        self.btYPlus = QtWidgets.QPushButton(self.groupBox_6)
        self.btYPlus.setGeometry(QtCore.QRect(730, 250, 191, 71))
        font = QtGui.QFont()
        font.setPointSize(17)
        font.setBold(True)
        font.setWeight(75)
        self.btYPlus.setFont(font)
        self.btYPlus.setAutoFillBackground(False)
        self.btYPlus.setStyleSheet("background-color: rgb(170, 170, 127);")
        self.btYPlus.setCheckable(False)
        self.btYPlus.setAutoDefault(False)
        self.btYPlus.setObjectName("btYPlus")
        self.btWidthPlus = QtWidgets.QPushButton(self.groupBox_6)
        self.btWidthPlus.setGeometry(QtCore.QRect(10, 30, 191, 71))
        font = QtGui.QFont()
        font.setPointSize(17)
        font.setBold(True)
        font.setWeight(75)
        self.btWidthPlus.setFont(font)
        self.btWidthPlus.setAutoFillBackground(False)
        self.btWidthPlus.setStyleSheet("background-color: rgb(170, 170, 127);")
        self.btWidthPlus.setCheckable(False)
        self.btWidthPlus.setAutoDefault(False)
        self.btWidthPlus.setObjectName("btWidthPlus")
        self.btHeightPlus = QtWidgets.QPushButton(self.groupBox_6)
        self.btHeightPlus.setGeometry(QtCore.QRect(10, 270, 191, 71))
        font = QtGui.QFont()
        font.setPointSize(17)
        font.setBold(True)
        font.setWeight(75)
        self.btHeightPlus.setFont(font)
        self.btHeightPlus.setAutoFillBackground(False)
        self.btHeightPlus.setStyleSheet("background-color: rgb(170, 170, 127);")
        self.btHeightPlus.setCheckable(False)
        self.btHeightPlus.setAutoDefault(False)
        self.btHeightPlus.setObjectName("btHeightPlus")
        self.btHeightMinus = QtWidgets.QPushButton(self.groupBox_6)
        self.btHeightMinus.setGeometry(QtCore.QRect(10, 360, 191, 71))
        font = QtGui.QFont()
        font.setPointSize(17)
        font.setBold(True)
        font.setWeight(75)
        self.btHeightMinus.setFont(font)
        self.btHeightMinus.setAutoFillBackground(False)
        self.btHeightMinus.setStyleSheet("background-color: rgb(170, 170, 127);")
        self.btHeightMinus.setCheckable(False)
        self.btHeightMinus.setAutoDefault(False)
        self.btHeightMinus.setObjectName("btHeightMinus")
        self.btWidthMinus = QtWidgets.QPushButton(self.groupBox_6)
        self.btWidthMinus.setGeometry(QtCore.QRect(10, 120, 191, 71))
        font = QtGui.QFont()
        font.setPointSize(17)
        font.setBold(True)
        font.setWeight(75)
        self.btWidthMinus.setFont(font)
        self.btWidthMinus.setAutoFillBackground(False)
        self.btWidthMinus.setStyleSheet("background-color: rgb(170, 170, 127);")
        self.btWidthMinus.setCheckable(False)
        self.btWidthMinus.setAutoDefault(False)
        self.btWidthMinus.setObjectName("btWidthMinus")
        self.label_17 = QtWidgets.QLabel(self.groupBox_6)
        self.label_17.setGeometry(QtCore.QRect(760, 460, 151, 41))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.label_17.setFont(font)
        self.label_17.setFrameShape(QtWidgets.QFrame.Box)
        self.label_17.setObjectName("label_17")
        self.label_34 = QtWidgets.QLabel(self.groupBox_6)
        self.label_34.setGeometry(QtCore.QRect(260, 530, 381, 31))
        self.label_34.setObjectName("label_34")
        self.horizontalLayout_4.addWidget(self.groupBox_6)
        self.horizontalLayout_2.addLayout(self.horizontalLayout_4)
        self.groupBox_2 = QtWidgets.QGroupBox(self.verticalLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.groupBox_2.setFont(font)
        self.groupBox_2.setAutoFillBackground(False)
        self.groupBox_2.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_2.setObjectName("groupBox_2")
        self.btReset = QtWidgets.QPushButton(self.groupBox_2)
        self.btReset.setGeometry(QtCore.QRect(280, 120, 161, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btReset.setFont(font)
        self.btReset.setAutoFillBackground(False)
        self.btReset.setStyleSheet("background-color: rgb(255, 255, 127);")
        self.btReset.setCheckable(False)
        self.btReset.setAutoDefault(False)
        self.btReset.setObjectName("btReset")
        self.groupBox = QtWidgets.QGroupBox(self.groupBox_2)
        self.groupBox.setGeometry(QtCore.QRect(0, 450, 931, 311))
        self.groupBox.setObjectName("groupBox")
        self.doubleSpinBox = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBox.setGeometry(QtCore.QRect(750, 140, 171, 31))
        self.doubleSpinBox.setAlignment(QtCore.Qt.AlignCenter)
        self.doubleSpinBox.setDecimals(2)
        self.doubleSpinBox.setMinimum(-99.0)
        self.doubleSpinBox.setSingleStep(1.0)
        self.doubleSpinBox.setObjectName("doubleSpinBox")
        self.label_20 = QtWidgets.QLabel(self.groupBox)
        self.label_20.setGeometry(QtCore.QRect(590, 140, 71, 31))
        self.label_20.setObjectName("label_20")
        self.doubleSpinBox_2 = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBox_2.setGeometry(QtCore.QRect(750, 190, 171, 31))
        self.doubleSpinBox_2.setAlignment(QtCore.Qt.AlignCenter)
        self.doubleSpinBox_2.setMinimum(-99.0)
        self.doubleSpinBox_2.setObjectName("doubleSpinBox_2")
        self.label_21 = QtWidgets.QLabel(self.groupBox)
        self.label_21.setGeometry(QtCore.QRect(590, 190, 71, 31))
        self.label_21.setObjectName("label_21")
        self.layoutWidget = QtWidgets.QWidget(self.groupBox)
        self.layoutWidget.setGeometry(QtCore.QRect(10, 40, 261, 266))
        self.layoutWidget.setObjectName("layoutWidget")
        self.gridLayout = QtWidgets.QGridLayout(self.layoutWidget)
        self.gridLayout.setContentsMargins(0, 0, 0, 0)
        self.gridLayout.setObjectName("gridLayout")
        self.tbThetaY = QtWidgets.QLineEdit(self.layoutWidget)
        font = QtGui.QFont()
        font.setFamily("Bahnschrift SemiBold")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.tbThetaY.setFont(font)
        self.tbThetaY.setAlignment(QtCore.Qt.AlignCenter)
        self.tbThetaY.setReadOnly(True)
        self.tbThetaY.setObjectName("tbThetaY")
        self.gridLayout.addWidget(self.tbThetaY, 1, 2, 1, 1)
        self.tbThetaZ = QtWidgets.QLineEdit(self.layoutWidget)
        font = QtGui.QFont()
        font.setFamily("Bahnschrift SemiBold")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.tbThetaZ.setFont(font)
        self.tbThetaZ.setAlignment(QtCore.Qt.AlignCenter)
        self.tbThetaZ.setReadOnly(True)
        self.tbThetaZ.setObjectName("tbThetaZ")
        self.gridLayout.addWidget(self.tbThetaZ, 2, 2, 1, 1)
        self.label_12 = QtWidgets.QLabel(self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(True)
        font.setWeight(75)
        self.label_12.setFont(font)
        self.label_12.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_12.setObjectName("label_12")
        self.gridLayout.addWidget(self.label_12, 3, 0, 1, 1)
        self.tbAxisX = QtWidgets.QLineEdit(self.layoutWidget)
        font = QtGui.QFont()
        font.setFamily("Bahnschrift SemiBold")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.tbAxisX.setFont(font)
        self.tbAxisX.setAlignment(QtCore.Qt.AlignCenter)
        self.tbAxisX.setReadOnly(True)
        self.tbAxisX.setObjectName("tbAxisX")
        self.gridLayout.addWidget(self.tbAxisX, 3, 2, 1, 1)
        self.label_13 = QtWidgets.QLabel(self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(True)
        font.setWeight(75)
        self.label_13.setFont(font)
        self.label_13.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_13.setObjectName("label_13")
        self.gridLayout.addWidget(self.label_13, 4, 0, 1, 1)
        self.tbAxisY = QtWidgets.QLineEdit(self.layoutWidget)
        font = QtGui.QFont()
        font.setFamily("Bahnschrift SemiBold")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.tbAxisY.setFont(font)
        self.tbAxisY.setAlignment(QtCore.Qt.AlignCenter)
        self.tbAxisY.setReadOnly(True)
        self.tbAxisY.setObjectName("tbAxisY")
        self.gridLayout.addWidget(self.tbAxisY, 4, 2, 1, 1)
        self.label_14 = QtWidgets.QLabel(self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(True)
        font.setWeight(75)
        self.label_14.setFont(font)
        self.label_14.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_14.setObjectName("label_14")
        self.gridLayout.addWidget(self.label_14, 5, 0, 1, 1)
        self.tbAxisZ = QtWidgets.QLineEdit(self.layoutWidget)
        font = QtGui.QFont()
        font.setFamily("Bahnschrift SemiBold")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.tbAxisZ.setFont(font)
        self.tbAxisZ.setAlignment(QtCore.Qt.AlignCenter)
        self.tbAxisZ.setReadOnly(True)
        self.tbAxisZ.setObjectName("tbAxisZ")
        self.gridLayout.addWidget(self.tbAxisZ, 5, 2, 1, 1)
        self.label_9 = QtWidgets.QLabel(self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(True)
        font.setWeight(75)
        self.label_9.setFont(font)
        self.label_9.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_9.setObjectName("label_9")
        self.gridLayout.addWidget(self.label_9, 2, 0, 1, 2)
        self.label_7 = QtWidgets.QLabel(self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(True)
        font.setWeight(75)
        self.label_7.setFont(font)
        self.label_7.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_7.setObjectName("label_7")
        self.gridLayout.addWidget(self.label_7, 1, 0, 1, 1)
        self.label_8 = QtWidgets.QLabel(self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(True)
        font.setWeight(75)
        self.label_8.setFont(font)
        self.label_8.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_8.setObjectName("label_8")
        self.gridLayout.addWidget(self.label_8, 0, 0, 1, 1)
        self.tbThetaX = QtWidgets.QLineEdit(self.layoutWidget)
        font = QtGui.QFont()
        font.setFamily("Bahnschrift SemiBold")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.tbThetaX.setFont(font)
        self.tbThetaX.setAlignment(QtCore.Qt.AlignCenter)
        self.tbThetaX.setReadOnly(True)
        self.tbThetaX.setObjectName("tbThetaX")
        self.gridLayout.addWidget(self.tbThetaX, 0, 2, 1, 1)
        self.label_23 = QtWidgets.QLabel(self.groupBox)
        self.label_23.setGeometry(QtCore.QRect(590, 40, 141, 31))
        self.label_23.setObjectName("label_23")
        self.label_24 = QtWidgets.QLabel(self.groupBox)
        self.label_24.setGeometry(QtCore.QRect(590, 90, 161, 31))
        self.label_24.setObjectName("label_24")
        self.lineEdit_7 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_7.setGeometry(QtCore.QRect(750, 40, 171, 31))
        self.lineEdit_7.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEdit_7.setObjectName("lineEdit_7")
        self.lineEdit_8 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_8.setGeometry(QtCore.QRect(750, 90, 171, 31))
        self.lineEdit_8.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEdit_8.setObjectName("lineEdit_8")
        self.label_25 = QtWidgets.QLabel(self.groupBox)
        self.label_25.setGeometry(QtCore.QRect(350, 40, 121, 31))
        self.label_25.setObjectName("label_25")
        self.lTocdoRB = QtWidgets.QLineEdit(self.groupBox)
        self.lTocdoRB.setGeometry(QtCore.QRect(320, 80, 171, 31))
        self.lTocdoRB.setAlignment(QtCore.Qt.AlignCenter)
        self.lTocdoRB.setObjectName("lTocdoRB")
        self.label_26 = QtWidgets.QLabel(self.groupBox)
        self.label_26.setGeometry(QtCore.QRect(340, 130, 141, 31))
        self.label_26.setObjectName("label_26")
        self.lTocdoBT = QtWidgets.QLineEdit(self.groupBox)
        self.lTocdoBT.setGeometry(QtCore.QRect(320, 170, 171, 31))
        self.lTocdoBT.setAlignment(QtCore.Qt.AlignCenter)
        self.lTocdoBT.setObjectName("lTocdoBT")
        self.lSoluong = QtWidgets.QLineEdit(self.groupBox)
        self.lSoluong.setGeometry(QtCore.QRect(320, 260, 171, 31))
        self.lSoluong.setAlignment(QtCore.Qt.AlignCenter)
        self.lSoluong.setObjectName("lSoluong")
        self.label_27 = QtWidgets.QLabel(self.groupBox)
        self.label_27.setGeometry(QtCore.QRect(320, 220, 201, 31))
        self.label_27.setObjectName("label_27")
        self.label_30 = QtWidgets.QLabel(self.groupBox)
        self.label_30.setGeometry(QtCore.QRect(540, 270, 311, 31))
        self.label_30.setObjectName("label_30")
        self.btStop = QtWidgets.QPushButton(self.groupBox_2)
        self.btStop.setGeometry(QtCore.QRect(10, 120, 161, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btStop.setFont(font)
        self.btStop.setAutoFillBackground(False)
        self.btStop.setStyleSheet("background-color: rgb(170, 0, 0);")
        self.btStop.setCheckable(False)
        self.btStop.setAutoDefault(False)
        self.btStop.setObjectName("btStop")
        self.btStart = QtWidgets.QPushButton(self.groupBox_2)
        self.btStart.setGeometry(QtCore.QRect(10, 30, 161, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btStart.setFont(font)
        self.btStart.setAutoFillBackground(False)
        self.btStart.setStyleSheet("background-color: rgb(231, 255, 215);")
        self.btStart.setCheckable(False)
        self.btStart.setAutoDefault(False)
        self.btStart.setObjectName("btStart")
        self.btHome = QtWidgets.QPushButton(self.groupBox_2)
        self.btHome.setGeometry(QtCore.QRect(280, 30, 161, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btHome.setFont(font)
        self.btHome.setAutoFillBackground(False)
        self.btHome.setStyleSheet("background-color: rgb(255, 255, 127);")
        self.btHome.setCheckable(False)
        self.btHome.setAutoDefault(False)
        self.btHome.setObjectName("btHome")
        self.groupBox_4 = QtWidgets.QGroupBox(self.groupBox_2)
        self.groupBox_4.setGeometry(QtCore.QRect(500, 10, 421, 451))
        self.groupBox_4.setObjectName("groupBox_4")
        self.btConnect = QtWidgets.QPushButton(self.groupBox_4)
        self.btConnect.setGeometry(QtCore.QRect(10, 40, 201, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btConnect.setFont(font)
        self.btConnect.setStyleSheet("background-color: rgb(85, 255, 0);")
        self.btConnect.setObjectName("btConnect")
        self.btDisconnect = QtWidgets.QPushButton(self.groupBox_4)
        self.btDisconnect.setGeometry(QtCore.QRect(10, 140, 201, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btDisconnect.setFont(font)
        self.btDisconnect.setStyleSheet("background-color: rgb(255, 85, 0);")
        self.btDisconnect.setObjectName("btDisconnect")
        self.machineStatus = QtWidgets.QLabel(self.groupBox_4)
        self.machineStatus.setGeometry(QtCore.QRect(80, 240, 161, 31))
        self.machineStatus.setObjectName("machineStatus")
        self.plcStatus = QtWidgets.QLabel(self.groupBox_4)
        self.plcStatus.setGeometry(QtCore.QRect(80, 280, 161, 31))
        self.plcStatus.setObjectName("plcStatus")
        self.lGreen = QtWidgets.QPushButton(self.groupBox_4)
        self.lGreen.setGeometry(QtCore.QRect(300, 40, 71, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.lGreen.setFont(font)
        self.lGreen.setAutoFillBackground(False)
        self.lGreen.setStyleSheet("background-color: rgb(255, 255, 255);")
        self.lGreen.setText("")
        self.lGreen.setCheckable(False)
        self.lGreen.setAutoDefault(False)
        self.lGreen.setObjectName("lGreen")
        self.lRed = QtWidgets.QPushButton(self.groupBox_4)
        self.lRed.setGeometry(QtCore.QRect(300, 140, 71, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.lRed.setFont(font)
        self.lRed.setAutoFillBackground(False)
        self.lRed.setStyleSheet("background-color: rgb(255, 255, 255);")
        self.lRed.setText("")
        self.lRed.setCheckable(False)
        self.lRed.setAutoDefault(False)
        self.lRed.setObjectName("lRed")
        self.label_31 = QtWidgets.QLabel(self.groupBox_4)
        self.label_31.setGeometry(QtCore.QRect(30, 400, 381, 31))
        self.label_31.setObjectName("label_31")
        self.plcStatus_2 = QtWidgets.QLabel(self.groupBox_4)
        self.plcStatus_2.setGeometry(QtCore.QRect(10, 280, 161, 31))
        self.plcStatus_2.setObjectName("plcStatus_2")
        self.machineStatus_2 = QtWidgets.QLabel(self.groupBox_4)
        self.machineStatus_2.setGeometry(QtCore.QRect(10, 240, 161, 31))
        self.machineStatus_2.setObjectName("machineStatus_2")
        self.groupBox_5 = QtWidgets.QGroupBox(self.groupBox_2)
        self.groupBox_5.setGeometry(QtCore.QRect(0, 250, 501, 201))
        self.groupBox_5.setObjectName("groupBox_5")
        self.btTech = QtWidgets.QPushButton(self.groupBox_5)
        self.btTech.setGeometry(QtCore.QRect(20, 120, 161, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btTech.setFont(font)
        self.btTech.setAutoFillBackground(False)
        self.btTech.setStyleSheet("background-color: rgb(231, 255, 215);")
        self.btTech.setCheckable(False)
        self.btTech.setAutoDefault(False)
        self.btTech.setObjectName("btTech")
        self.btVaccum = QtWidgets.QPushButton(self.groupBox_5)
        self.btVaccum.setGeometry(QtCore.QRect(20, 40, 161, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btVaccum.setFont(font)
        self.btVaccum.setAutoFillBackground(False)
        self.btVaccum.setStyleSheet("background-color: rgb(231, 255, 215);")
        self.btVaccum.setCheckable(False)
        self.btVaccum.setAutoDefault(False)
        self.btVaccum.setObjectName("btVaccum")
        self.btBangtai1 = QtWidgets.QPushButton(self.groupBox_5)
        self.btBangtai1.setGeometry(QtCore.QRect(310, 40, 161, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btBangtai1.setFont(font)
        self.btBangtai1.setAutoFillBackground(False)
        self.btBangtai1.setStyleSheet("background-color: rgb(231, 255, 215);")
        self.btBangtai1.setCheckable(False)
        self.btBangtai1.setAutoDefault(False)
        self.btBangtai1.setObjectName("btBangtai1")
        self.btBangtai2 = QtWidgets.QPushButton(self.groupBox_5)
        self.btBangtai2.setGeometry(QtCore.QRect(310, 120, 161, 71))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.btBangtai2.setFont(font)
        self.btBangtai2.setAutoFillBackground(False)
        self.btBangtai2.setStyleSheet("background-color: rgb(231, 255, 215);")
        self.btBangtai2.setCheckable(False)
        self.btBangtai2.setAutoDefault(False)
        self.btBangtai2.setObjectName("btBangtai2")
        self.horizontalLayout_2.addWidget(self.groupBox_2)
        self.verticalLayout_2.addLayout(self.horizontalLayout_2)
        self.verticalLayoutWidget_4 = QtWidgets.QWidget(self.centralwidget)
        self.verticalLayoutWidget_4.setGeometry(QtCore.QRect(0, 850, 2926, 145))
        self.verticalLayoutWidget_4.setObjectName("verticalLayoutWidget_4")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_4)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setContentsMargins(1, 0, 1050, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.lbStatus = QtWidgets.QLabel(self.verticalLayoutWidget_4)
        self.lbStatus.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(40)
        font.setBold(True)
        font.setWeight(75)
        self.lbStatus.setFont(font)
        self.lbStatus.setStyleSheet("background-color: rgb(255, 170, 0);")
        self.lbStatus.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.lbStatus.setTextFormat(QtCore.Qt.AutoText)
        self.lbStatus.setAlignment(QtCore.Qt.AlignCenter)
        self.lbStatus.setIndent(0)
        self.lbStatus.setTextInteractionFlags(QtCore.Qt.NoTextInteraction)
        self.lbStatus.setObjectName("lbStatus")
        self.horizontalLayout_5.addWidget(self.lbStatus)
        self.verticalLayout_4.addLayout(self.horizontalLayout_5)
        self.label_11 = QtWidgets.QLabel(self.centralwidget)
        self.label_11.setGeometry(QtCore.QRect(0, -20, 1881, 111))
        self.label_11.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(60)
        font.setBold(True)
        font.setWeight(75)
        self.label_11.setFont(font)
        self.label_11.setAutoFillBackground(False)
        self.label_11.setStyleSheet("background-color: rgb(255, 255, 255);")
        self.label_11.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.label_11.setAlignment(QtCore.Qt.AlignCenter)
        self.label_11.setIndent(0)
        self.label_11.setTextInteractionFlags(QtCore.Qt.NoTextInteraction)
        self.label_11.setObjectName("label_11")
        self.label_22 = QtWidgets.QLabel(self.centralwidget)
        self.label_22.setGeometry(QtCore.QRect(6, 0, 131, 91))
        self.label_22.setStyleSheet("")
        self.label_22.setFrameShape(QtWidgets.QFrame.Box)
        self.label_22.setText("")
        self.label_22.setPixmap(QtGui.QPixmap("C:/Users/<USER>/Downloads/Black_Autoparts_In_Gear__Auto_Piston__Spark_Plug_And_Wrench__Logo_Design__3_-removebg-preview.png"))
        self.label_22.setScaledContents(True)
        self.label_22.setObjectName("label_22")
        self.label_32 = QtWidgets.QLabel(self.centralwidget)
        self.label_32.setGeometry(QtCore.QRect(1760, 0, 121, 91))
        self.label_32.setStyleSheet("")
        self.label_32.setFrameShape(QtWidgets.QFrame.Box)
        self.label_32.setText("")
        self.label_32.setPixmap(QtGui.QPixmap("C:/Users/<USER>/Downloads/hihi.jpg"))
        self.label_32.setScaledContents(True)
        self.label_32.setObjectName("label_32")
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 2022, 21))
        self.menubar.setObjectName("menubar")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "DetalRobot Software bt Factory Automation"))
        self.groupBox_6.setTitle(_translate("MainWindow", "Image Process"))
        self.groupBox_3.setTitle(_translate("MainWindow", "Log File "))
        self.label.setText(_translate("MainWindow", "Khả năng phát hiện :"))
        self.label_2.setText(_translate("MainWindow", "Độ Sáng :"))
        self.label_3.setText(_translate("MainWindow", "Thông Số HSV :"))
        self.label_5.setText(_translate("MainWindow", "UH"))
        self.label_6.setText(_translate("MainWindow", "UV"))
        self.label_10.setText(_translate("MainWindow", "US"))
        self.label_16.setText(_translate("MainWindow", "DH"))
        self.label_18.setText(_translate("MainWindow", "DV"))
        self.label_19.setText(_translate("MainWindow", "DS"))
        self.numPH.setText(_translate("MainWindow", "Value"))
        self.numDS.setText(_translate("MainWindow", "Value"))
        self.label_33.setText(_translate("MainWindow", "Note : Tất cả Setting được lưu vào file Setting.text cùng thư mục"))
        self.tbX.setText(_translate("MainWindow", "0"))
        self.label_15.setText(_translate("MainWindow", "<html><head/><body><p>Position X</p></body></html>"))
        self.tbY.setText(_translate("MainWindow", "0"))
        self.btXPlus.setText(_translate("MainWindow", "LX"))
        self.btYMinus.setText(_translate("MainWindow", "DY"))
        self.btXMinus.setText(_translate("MainWindow", "RX "))
        self.btYPlus.setText(_translate("MainWindow", "UY"))
        self.btWidthPlus.setText(_translate("MainWindow", "WX +"))
        self.btHeightPlus.setText(_translate("MainWindow", "HY +"))
        self.btHeightMinus.setText(_translate("MainWindow", "HY -"))
        self.btWidthMinus.setText(_translate("MainWindow", "WX -"))
        self.label_17.setText(_translate("MainWindow", "<html><head/><body><p>Position Y</p></body></html>"))
        self.label_34.setText(_translate("MainWindow", "<html><head/><body><p>Note : Chọn Vùng ROI phù hợp phát hiện sản phẩm</p></body></html>"))
        self.groupBox_2.setTitle(_translate("MainWindow", "Control Panel"))
        self.btReset.setText(_translate("MainWindow", "RESET"))
        self.groupBox.setTitle(_translate("MainWindow", "Setting Delta"))
        self.label_20.setText(_translate("MainWindow", "Offset X:"))
        self.label_21.setText(_translate("MainWindow", "Offset Y:"))
        self.tbThetaY.setText(_translate("MainWindow", "0"))
        self.tbThetaZ.setText(_translate("MainWindow", "0"))
        self.label_12.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Axis X</p></body></html>"))
        self.tbAxisX.setText(_translate("MainWindow", "0"))
        self.label_13.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Axis Y</p></body></html>"))
        self.tbAxisY.setText(_translate("MainWindow", "0"))
        self.label_14.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Axis Z</p></body></html>"))
        self.tbAxisZ.setText(_translate("MainWindow", "0"))
        self.label_9.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Theta Z</p></body></html>"))
        self.label_7.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Theta Y</p></body></html>"))
        self.label_8.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Theta X</p></body></html>"))
        self.tbThetaX.setText(_translate("MainWindow", "0"))
        self.label_23.setText(_translate("MainWindow", "Set Số Sản Lượng :"))
        self.label_24.setText(_translate("MainWindow", "Sản Lượng Hiện Tại :"))
        self.lineEdit_7.setText(_translate("MainWindow", "0"))
        self.lineEdit_8.setText(_translate("MainWindow", "0"))
        self.label_25.setText(_translate("MainWindow", "Tốc độ Robot :"))
        self.lTocdoRB.setText(_translate("MainWindow", "0.0"))
        self.label_26.setText(_translate("MainWindow", "Tốc Độ Bẳng Tải :"))
        self.lTocdoBT.setText(_translate("MainWindow", "0.0"))
        self.lSoluong.setText(_translate("MainWindow", "0"))
        self.label_27.setText(_translate("MainWindow", "Sản Phẩm Trên Băng Tải"))
        self.label_30.setText(_translate("MainWindow", "<html><head/><body><p>Note : Offset Robot Khi Cân Chỉnh Camera</p></body></html>"))
        self.btStop.setText(_translate("MainWindow", "STOP"))
        self.btStart.setText(_translate("MainWindow", "START"))
        self.btHome.setText(_translate("MainWindow", "HOME"))
        self.groupBox_4.setTitle(_translate("MainWindow", "Status Connect"))
        self.btConnect.setText(_translate("MainWindow", "Connect"))
        self.btDisconnect.setText(_translate("MainWindow", "Disconnect"))
        self.machineStatus.setText(_translate("MainWindow", "<html><head/><body><p>Disconnected</p></body></html>"))
        self.plcStatus.setText(_translate("MainWindow", "<html><head/><body><p> _______________</p></body></html>"))
        self.label_31.setText(_translate("MainWindow", "<html><head/><body><p>Note : Vui lòng kiểm tra Robot trước khi vận hành</p></body></html>"))
        self.plcStatus_2.setText(_translate("MainWindow", "<html><head/><body><p>PLC :</p></body></html>"))
        self.machineStatus_2.setText(_translate("MainWindow", "<html><head/><body><p>Status :</p></body></html>"))
        self.groupBox_5.setTitle(_translate("MainWindow", "Manual "))
        self.btTech.setText(_translate("MainWindow", "Teching"))
        self.btVaccum.setText(_translate("MainWindow", "Vaccum"))
        self.btBangtai1.setText(_translate("MainWindow", "Băng Tải 1"))
        self.btBangtai2.setText(_translate("MainWindow", "Băng Tải 2"))
        self.lbStatus.setText(_translate("MainWindow", "<html><head/><body><p align=\"center\">Vui Lòng Kết Nối Chương Trình Với Robot Delta </p></body></html>"))
        self.label_11.setText(_translate("MainWindow", "<html><head/><body><p><span style=\" color:#00aa7f;\">DELTA ROBOT SOFTWARE</span></p></body></html>"))


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    MainWindow = QtWidgets.QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(MainWindow)
    MainWindow.show()
    sys.exit(app.exec_())
